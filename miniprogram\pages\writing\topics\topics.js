Page({
  data: {
    moduleId: '',
    moduleTitle: '',
    topics: [
      {
        id: 'culture_communication',
        title: '文化习俗/跨文化交流',
        icon: '🌍',
        color: 'blue',
        description: '了解不同文化背景，提升跨文化交流能力'
      },
      {
        id: 'campus_life',
        title: '青少年校园生活/社会生活/成长与选择',
        icon: '🎓',
        color: 'green',
        description: '校园生活点滴，成长过程中的选择与思考'
      },
      {
        id: 'education_learning',
        title: '教育学习',
        icon: '📚',
        color: 'purple',
        description: '教育理念、学习方法与教育改革'
      },
      {
        id: 'technology_media',
        title: '科技媒体',
        icon: '💻',
        color: 'cyan',
        description: '科技发展对生活的影响，媒体与信息时代'
      },
      {
        id: 'environment_protection',
        title: '环境保护',
        icon: '🌱',
        color: 'emerald',
        description: '环保意识，可持续发展与生态保护'
      },
      {
        id: 'sports_health',
        title: '体育健康',
        icon: '🏃',
        color: 'orange',
        description: '运动健身，健康生活方式与体育精神'
      },
      {
        id: 'art',
        title: '艺术',
        icon: '🎨',
        color: 'pink',
        description: '艺术欣赏，创作灵感与艺术文化'
      },
      {
        id: 'interpersonal_relationship',
        title: '人际关系',
        icon: '👥',
        color: 'indigo',
        description: '友谊、家庭关系与社交技能'
      },
      {
        id: 'public_welfare',
        title: '公益活动',
        icon: '❤️',
        color: 'red',
        description: '志愿服务，社会责任与爱心奉献'
      },
      {
        id: 'travel',
        title: '旅行',
        icon: '✈️',
        color: 'sky',
        description: '旅游见闻，文化体验与人生感悟'
      },
      {
        id: 'literature',
        title: '文学',
        icon: '📖',
        color: 'violet',
        description: '文学作品赏析，阅读感悟与写作技巧'
      },
      {
        id: 'career_major',
        title: '职业/大学专业',
        icon: '💼',
        color: 'amber',
        description: '职业规划，专业选择与未来发展'
      }
    ]
  },

  onLoad(options) {
    const { moduleId, moduleTitle } = options;
    this.setData({
      moduleId: moduleId || '',
      moduleTitle: moduleTitle || ''
    });
    
    wx.setNavigationBarTitle({
      title: moduleTitle || '话题选择'
    });
  },

  // 选择话题
  onTopicSelect(e) {
    const topicId = e.currentTarget.dataset.id;
    const selectedTopic = this.data.topics.find(topic => topic.id === topicId);
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 显示开发中提示
    wx.showModal({
      title: selectedTopic.title,
      content: '该话题内容正在完善中，敬请期待！\n\n注意：这只是示例内容，不是正式内容。',
      showCancel: false,
      confirmText: '知道了',
      confirmColor: '#8A2BE2'
    });
  }
}); 