const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 登录模式：account（账号密码）、wechat（微信登录）
    loginMode: 'account',
    
    // 账号密码登录
    username: '',
    password: '',
    showPassword: false,
    
    // 服务条款
    agreeTerms: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('登录页面加载');
    
    // 检查是否已经登录
    const app = getApp();
    if (app.isLoggedIn()) {
      // 如果已登录，跳转到首页
      wx.reLaunch({
        url: '/pages/index/index'
      });
      return;
    }
    
    // 设置默认为账号密码登录
    this.setData({
      loginMode: 'account'
    });
  },

  // 切换登录模式
  switchLoginMode(e) {
    const mode = e.currentTarget.dataset.mode;
    console.log('切换登录模式:', mode);
    
    this.setData({
      loginMode: mode,
      // 清空之前的输入
      username: '',
      password: '',
      showPassword: false
    });
  },

  // 输入事件处理
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    });
  },

  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示
  togglePasswordVisibility() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 协议同意状态变化
  onAgreeTermsChange(e) {
    console.log('=== 协议复选框事件触发 ===');
    console.log('事件详情:', e.detail);
    console.log('选中的值数组:', e.detail.value);
    console.log('修改前状态:', this.data.agreeTerms);

    // checkbox-group 返回选中项的值数组
    const isChecked = e.detail.value.length > 0;

    this.setData({
      agreeTerms: isChecked
    }, () => {
      console.log('修改后状态:', this.data.agreeTerms);
    });
  },

  // 主登录方法
  async handleLogin() {
    if (this.data.loading) return;

    // 检查是否同意协议
    if (!this.data.agreeTerms) {
      wx.showToast({
        title: '请先阅读并同意用户协议和隐私政策',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    const { loginMode } = this.data;

    try {
      this.setData({ loading: true });

      if (loginMode === 'account') {
        await this.handleAccountLogin();
      } else if (loginMode === 'wechat') {
        await this.handleWechatLogin();
      }
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },

  // 账号密码登录
  async handleAccountLogin() {
    const { username, password } = this.data;

    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '登录中...' });

    const res = await wx.cloud.callFunction({
      name: 'login',
      data: { username, password }
    });

    if (res.result.code === 200) {
      const success = app.setUserInfo(res.result.data.userInfo, res.result.data.token);
      if (success) {
        wx.showToast({ title: '登录成功', icon: 'success' });
        setTimeout(() => {
          wx.reLaunch({ url: '/pages/index/index' });
        }, 1500);
      } else {
        throw new Error('保存登录信息失败');
      }
    } else {
      wx.showToast({
        title: res.result.message || '登录失败',
        icon: 'none'
      });
    }
    wx.hideLoading();
  },

  // 微信登录 - 使用新的方式
  async handleWechatLogin() {
    console.log('=== 微信登录点击 ===');
    console.log('当前协议同意状态:', this.data.agreeTerms);

    // 检查是否同意协议
    if (!this.data.agreeTerms) {
      console.log('用户未同意协议，阻止登录');
      wx.showToast({
        title: '请先阅读并同意用户协议和隐私政策',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    console.log('协议检查通过，继续登录流程');

    try {
      // 1. 先进行微信登录获取code
      console.log('1. 开始微信登录...');
      const loginRes = await wx.login();
      if (!loginRes.code) {
        throw new Error('微信登录失败');
      }
      console.log('1. 微信登录成功，code:', loginRes.code);

      wx.showLoading({
        title: '登录中...'
      });

      // 2. 调用云函数进行登录（不需要用户信息）
      console.log('2. 调用云函数登录...');
      const result = await wx.cloud.callFunction({
        name: 'wechatLogin',
        data: {
          code: loginRes.code,
          userInfo: null // 不传用户信息
        }
      });

      console.log('2. 云函数返回结果:', result);

      if (result.result.success) {
        const { userInfo, token, openid } = result.result.data;
        
        // 3. 保存用户信息到本地存储 - 使用应用的标准方法
        const app = getApp();
        const saveSuccess = app.setUserInfo(userInfo, token);
        
        if (!saveSuccess) {
          throw new Error('保存用户信息失败');
        }
        
        // 额外保存openid
        wx.setStorageSync('openid', openid);
        
        // 确保全局状态已更新
        console.log('登录后全局状态检查:', {
          isLoggedIn: app.isLoggedIn(),
          userInfo: app.globalData.userInfo,
          token: app.globalData.token
        });

        wx.hideLoading();
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });

        // 4. 检查是否需要完善信息（只对新用户或信息不完整的用户提示）
        const needCompleteInfo = !userInfo.wechatInfo?.nickName || 
                                 userInfo.wechatInfo?.nickName === '' || 
                                 userInfo.wechatInfo?.nickName.startsWith('微信用户') ||
                                 !userInfo.username ||
                                 userInfo.username === '';
        
        console.log('用户信息完整性检查:', {
          needCompleteInfo,
          nickName: userInfo.wechatInfo?.nickName,
          username: userInfo.username
        });

        if (needCompleteInfo) {
          // 只对新用户或信息不完整的用户提示
          const completeInfoRes = await new Promise((resolve) => {
            wx.showModal({
              title: '完善个人信息',
              content: '为了更好的使用体验，建议您完善个人信息（头像、昵称等）。',
              confirmText: '去完善',
              cancelText: '跳过',
              success: resolve
            });
          });

          if (completeInfoRes.confirm) {
            // 跳转到我的页面，并设置标记来显示完善信息弹窗
            wx.switchTab({
              url: '/pages/profile/profile'
            });
            
            // 延迟一下再跳转到个人中心
            setTimeout(() => {
              wx.navigateTo({
                url: '/pages/profile/personal-center/personal-center?action=complete&fromLogin=1'
              });
            }, 500);
          } else {
            // 直接跳转到主页
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        } else {
          // 老用户直接跳转到主页
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/index/index'
            });
          }, 1500);
        }

      } else {
        throw new Error(result.result.message || '登录失败');
      }

    } catch (error) {
      console.error('微信登录失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    }
  },

  // 注册跳转
  handleRegister() {
    wx.navigateTo({
      url: '/pages/register/register'
    });
  },

  // 显示用户协议
  showAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/user'
    });
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/agreement/privacy'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '墨词自习室 - 师生互动学习空间',
      path: '/pages/index/index',
      imageUrl: '/assets/icons/logo.png'
    };
  }
}); 