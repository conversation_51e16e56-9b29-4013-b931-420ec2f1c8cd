Page({
  data: {
    articleId: '',
    type: '', // foreign-media 或 classic-novels
    article: null,
    paragraphs: [], // 处理后的段落数据，包含显示状态
    showAllChinese: false // 是否显示所有中文
  },

  onLoad(options) {
    const { articleId, type } = options;
    this.setData({
      articleId: articleId || '',
      type: type || ''
    });
    
    this.loadArticleData();
  },

  // 加载文章数据
  loadArticleData() {
    const { articleId, type } = this.data;
    
    if (type === 'foreign-media') {
      this.loadForeignMediaData();
    } else if (type === 'classic-novels') {
      this.loadClassicNovelData();
    }
  },

  // 加载外刊数据
  loadForeignMediaData() {
    // 这里应该从外刊页面获取数据，简化处理，直接根据ID匹配
    const articles = [
      {
        id: 'tech_ai_future',
        title: 'The Future of Artificial Intelligence in Education',
        source: 'Tech Weekly',
        date: '2024-01-15',
        content: [
          {
            english: 'Artificial Intelligence (AI) is revolutionizing the education sector in unprecedented ways. From personalized learning experiences to automated grading systems, AI technology is transforming how students learn and teachers teach.',
            chinese: '人工智能(AI)正在以前所未有的方式革命性地改变教育领域。从个性化学习体验到自动评分系统，AI技术正在改变学生学习和教师教学的方式。'
          },
          {
            english: 'One of the most significant impacts of AI in education is the ability to provide personalized learning paths for individual students. Traditional one-size-fits-all approaches are being replaced by adaptive learning systems that adjust content difficulty and pace based on each student\'s performance and learning style.',
            chinese: 'AI在教育中最重要的影响之一是能够为每个学生提供个性化的学习路径。传统的一刀切方法正在被自适应学习系统所取代，这些系统根据每个学生的表现和学习风格调整内容难度和节奏。'
          },
          {
            english: 'However, the integration of AI in education also raises important questions about data privacy, the role of human teachers, and the digital divide. As we move forward, it will be crucial to address these challenges while harnessing the tremendous potential of AI to enhance educational outcomes.',
            chinese: '然而，AI在教育中的整合也引发了关于数据隐私、人类教师角色和数字鸿沟的重要问题。在向前发展的过程中，在利用AI巨大潜力来提升教育成果的同时，解决这些挑战将是至关重要的。'
          }
        ]
      }
      // 其他文章数据...
    ];

    const article = articles.find(item => item.id === this.data.articleId);
    if (article) {
      this.processArticleData(article);
    }
  },

  // 加载名著小说数据
  loadClassicNovelData() {
    const novels = [
      {
        id: 'wuthering_heights',
        title: '呼啸山庄 (Wuthering Heights)',
        author: 'Emily Brontë',
        content: [
          {
            english: 'I have just returned from a visit to my landlord—the solitary neighbour that I shall be troubled with. This is certainly a beautiful country! In all England, I do not believe that I could have fixed on a situation so completely removed from the stir of society.',
            chinese: '我刚刚拜访了我的房东归来——他是我唯一需要打扰的邻居。这确实是个美丽的地方！在整个英格兰，我相信我不可能找到一个如此完全远离社会喧嚣的地方。'
          },
          {
            english: 'A perfect misanthropist\'s heaven: and Mr. Heathcliff and I are such a suitable pair to divide the desolation between us. A capital fellow! He little imagined how my heart warmed towards him when I beheld his black eyes withdraw so suspiciously under their brows.',
            chinese: '这简直是厌世者的天堂：希斯克利夫先生和我正是分享这份荒凉的合适人选。多么出色的家伙！当我看到他那双黑眼睛如此可疑地从眉毛下缩回时，他绝想不到我的心是多么向往他。'
          }
        ]
      },
      {
        id: 'little_women',
        title: '小妇人 (Little Women)',
        author: 'Louisa May Alcott',
        content: [
          {
            english: '"Christmas won\'t be Christmas without any presents," grumbled Jo, lying on the rug. "It\'s so dreadful to be poor!" sighed Meg, looking down at her old dress.',
            chinese: '"没有礼物的圣诞节就不是圣诞节，"乔趴在地毯上抱怨道。"贫穷真是太可怕了！"梅格叹息着，低头看着自己的旧裙子。'
          },
          {
            english: '"I don\'t think it\'s fair for some girls to have plenty of pretty things, and other girls nothing at all," added little Amy, with an injured sniff.',
            chinese: '"我觉得有些女孩拥有很多漂亮的东西，而另一些女孩什么都没有，这是不公平的，"小艾米委屈地抽了抽鼻子补充道。'
          }
        ]
      },
      {
        id: 'great_gatsby',
        title: '了不起的盖茨比 (The Great Gatsby)',
        author: 'F. Scott Fitzgerald',
        content: [
          {
            english: 'In my younger and more vulnerable years my father gave me some advice that I\'ve carried with me ever since. "Whenever you feel like criticizing anyone," he told me, "just remember that all the people in this world haven\'t had the advantages that you\'ve had."',
            chinese: '在我年轻而又脆弱的岁月里，父亲给了我一些建议，我至今还铭记在心。"每当你想要批评别人的时候，"他对我说，"只要记住，这世上并不是所有人都有你这样的优越条件。"'
          },
          {
            english: 'And so with the sunshine and the great bursts of leaves growing on the trees, just as things grow in fast movies, I had that familiar conviction that life was beginning over again with the summer.',
            chinese: '就这样，伴随着阳光和树上大片大片绽放的叶子，就像快进电影里的场景一样，我又有了那种熟悉的信念：生活将随着夏天的到来而重新开始。'
          }
        ]
      },
      {
        id: 'harry_potter',
        title: '哈利波特 (Harry Potter)',
        author: 'J.K. Rowling',
        content: [
          {
            english: 'Mr. and Mrs. Dursley of number four, Privet Drive, were proud to say that they were perfectly normal, thank you very much. They were the last people you\'d expect to be involved in anything strange or mysterious, because they just didn\'t hold with such nonsense.',
            chinese: '住在女贞路四号的德思礼夫妇总是骄傲地说他们是非常正常的人，谢谢。他们是最不可能卷入任何奇怪或神秘事件的人，因为他们根本不相信这些无稽之谈。'
          },
          {
            english: 'Mr. Dursley was the director of a firm called Grunnings, which made drills. He was a big, beefy man with hardly any neck, although he did have a very large mustache.',
            chinese: '德思礼先生是一家名叫格朗宁斯的钻头制造公司的董事。他是个大块头，肥胖，几乎没有脖子，不过他确实有一把很大的胡子。'
          }
        ]
      },
      {
        id: 'little_prince',
        title: '小王子 (The Little Prince)',
        author: 'Antoine de Saint-Exupéry',
        content: [
          {
            english: 'Once when I was six years old I saw a magnificent picture in a book, called True Stories from Nature, about the primeval forest. It was a picture of a boa constrictor in the act of swallowing an animal.',
            chinese: '当我六岁的时候，我在一本叫做《大自然的真实故事》的书中看到了一幅壮观的图画，是关于原始森林的。那是一幅描绘蟒蛇正在吞噬动物的图画。'
          },
          {
            english: 'In the book it said: "Boa constrictors swallow their prey whole, without chewing it. After that they are not able to move, and they sleep through the six months that they need for digestion."',
            chinese: '书中写道："蟒蛇会把猎物整个吞下，不加咀嚼。之后它们无法移动，要睡上六个月来消化食物。"'
          }
        ]
      },
      {
        id: 'sun_also_rises',
        title: '太阳照常升起 (The Sun Also Rises)',
        author: 'Ernest Hemingway',
        content: [
          {
            english: 'Robert Cohn was once middleweight boxing champion of Princeton. Do not think that I am very much impressed by that as boxing, but it meant a lot to Cohn. He cared nothing for boxing, in fact he disliked it, but he learned it painfully and thoroughly to counteract the feeling of inferiority and shyness he had felt on being treated as a Jew at Princeton.',
            chinese: '罗伯特·科恩曾经是普林斯顿的中量级拳击冠军。不要以为我对此作为拳击运动有多么深刻的印象，但这对科恩来说意义重大。他对拳击毫无兴趣，事实上他讨厌拳击，但他痛苦而彻底地学会了它，以抵消他在普林斯顿因被当作犹太人对待而感到的自卑和羞怯。'
          },
          {
            english: 'He was a nice boy, a friendly boy, and very shy, and it made him bitter. He took it out in boxing, and he came out of Princeton with painful self-consciousness and the flattened nose, and was married by the first girl who was nice to him.',
            chinese: '他是个好孩子，友善的孩子，非常害羞，这让他变得痛苦。他通过拳击来发泄，带着痛苦的自我意识和被打扁的鼻子离开了普林斯顿，然后娶了第一个对他好的女孩。'
          }
        ]
      }
    ];

    const article = novels.find(item => item.id === this.data.articleId);
    if (article) {
      this.processArticleData(article);
    }
  },

  // 处理文章数据
  processArticleData(article) {
    const paragraphs = article.content.map((item, index) => ({
      ...item,
      id: index,
      showChinese: false // 默认不显示中文
    }));

    this.setData({
      article: article,
      paragraphs: paragraphs
    });

    wx.setNavigationBarTitle({
      title: article.title.split('(')[0].trim() || '文章阅读'
    });
  },

  // 切换单个段落的中文显示
  onToggleParagraph(e) {
    const index = e.currentTarget.dataset.index;
    const paragraphs = this.data.paragraphs;
    paragraphs[index].showChinese = !paragraphs[index].showChinese;
    
    this.setData({
      paragraphs: paragraphs
    });

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });
  },

  // 切换所有中文显示
  onToggleAllChinese() {
    const showAll = !this.data.showAllChinese;
    const paragraphs = this.data.paragraphs.map(item => ({
      ...item,
      showChinese: showAll
    }));

    this.setData({
      showAllChinese: showAll,
      paragraphs: paragraphs
    });

    // 添加触觉反馈
    wx.vibrateShort({
      type: 'medium'
    });
  }
}); 