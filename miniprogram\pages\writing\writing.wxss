/* 写作积累页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 模块容器 */
.modules-container {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 12rpx;
  border-left: 8rpx solid #8A2BE2;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.module-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.module-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.card-header {
  padding: 32rpx 24rpx 16rpx;
  background: linear-gradient(135deg, #8A2BE2 0%, #6A1B9A 100%);
  color: white;
  display: flex;
  align-items: center;
}

.module-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.module-info {
  flex: 1;
}

.module-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.module-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.card-content {
  padding: 24rpx;
}

.module-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}



/* 话题预览 */
.topics-preview {
  margin-bottom: 40rpx;
}

.topics-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.topic-tag {
  background: rgba(138, 43, 226, 0.1);
  border: 2rpx solid rgba(138, 43, 226, 0.3);
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
}

.topic-text {
  font-size: 24rpx;
  color: #8A2BE2;
}

/* 学习贴士 */
.tips-section {
  margin-bottom: 40rpx;
}

.tips-container {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-dot {
  color: #8A2BE2;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 开发提示 */
.development-notice {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-weight: bold;
  color: #856404;
  font-size: 28rpx;
}

.notice-content {
  color: #856404;
  font-size: 26rpx;
} 