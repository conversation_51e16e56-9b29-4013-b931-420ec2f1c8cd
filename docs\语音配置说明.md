# 🎤 语音配置说明

## 如何更改发音语音

### 方法1：修改默认语音（推荐）

编辑 `miniprogram/config/voice-config.js` 文件：

```javascript
// 默认语音设置
const DEFAULT_VOICE_CONFIG = {
  // 🎤 在这里修改默认语音
  defaultVoice: 'en-US-AriaNeural',  // 改成你想要的语音
  
  // 不同模式下的语音设置
  modeVoices: {
    practice: 'en-US-AriaNeural',    // 练习模式
    test: 'en-US-AriaNeural',        // 测试模式
    dictation: 'en-US-AriaNeural',   // 听写模式
    translation: 'en-US-AriaNeural'  // 翻译模式
  }
};
```

### 方法2：直接修改云函数调用

在各个页面的TTS调用中修改 `voice` 参数：

```javascript
wx.cloud.callFunction({
  name: 'ttsSpeak',
  data: {
    text: word,
    voice: 'en-GB-SoniaNeural', // 改成你想要的语音
    // ... 其他参数
  }
});
```

## 🎭 可用语音列表

### 美式英语 (American English)
- `en-US-AriaNeural` - 女声(Aria) ⭐ 默认
- `en-US-JennyNeural` - 女声(Jenny)
- `en-US-GuyNeural` - 男声(Guy)
- `en-US-DavisNeural` - 男声(Davis)

### 英式英语 (British English)
- `en-GB-SoniaNeural` - 女声(Sonia)
- `en-GB-LibbyNeural` - 女声(Libby)
- `en-GB-RyanNeural` - 男声(Ryan)
- `en-GB-ThomasNeural` - 男声(Thomas)

## 🔧 快速切换预设

使用预设名称快速切换：

```javascript
// 在 voice-config.js 中定义的预设
const VOICE_PRESETS = {
  american_female: 'en-US-AriaNeural',  // 美式女声
  american_male: 'en-US-GuyNeural',     // 美式男声
  british_female: 'en-GB-SoniaNeural',  // 英式女声
  british_male: 'en-GB-RyanNeural'      // 英式男声
};
```

## 📝 修改步骤

1. **选择语音**：从上面的列表中选择你喜欢的语音
2. **修改配置**：编辑 `voice-config.js` 或直接修改调用代码
3. **部署云函数**：右键点击 `cloudfunctions/ttsSpeak` → "上传并部署"
4. **测试**：在小程序中测试发音效果

## 🎯 推荐组合

- **标准学习**：`en-US-AriaNeural` (美式女声，清晰标准)
- **男声偏好**：`en-US-GuyNeural` (美式男声，深沉稳重)
- **英式发音**：`en-GB-SoniaNeural` (英式女声，优雅地道)
- **多样化练习**：可以为不同模式设置不同语音

## ⚠️ 注意事项

1. 修改语音后需要重新部署云函数
2. 所有语音都是免费的（在Azure免费额度内）
3. 不同语音的发音速度和音调可能略有差异
4. 建议选择一个主要语音保持一致性

## 🔍 测试方法

修改后可以通过以下方式测试：
1. 打开听写练习页面
2. 点击"测试TTS功能"按钮
3. 查看控制台日志确认使用的语音
4. 听取发音效果是否符合预期
