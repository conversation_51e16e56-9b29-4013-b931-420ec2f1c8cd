/* pages/profile/received/received.wxss */
.received-container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: white;
  text-align: center;
  margin-bottom: 20rpx;
  padding: 30rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 输入区域 */
.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-container {
  position: relative;
  margin-bottom: 20rpx;
}

.share-id-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 16rpx;
  padding: 0 100rpx 0 20rpx;
  font-size: 30rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.share-id-input:focus {
  border-color: #007AFF;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(0, 122, 255, 0.1);
}

.clear-btn {
  position: absolute;
  right: 15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background: #ccc;
  color: white;
  font-size: 24rpx;
  line-height: 50rpx;
  text-align: center;
  border: none;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: #999;
}

.join-btn {
  width: 100%;
  height: 80rpx;
  background: #007AFF;
  color: white;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.join-btn::after {
  border: none;
}

.join-btn:disabled {
  background: #e0e0e0;
  color: #999;
  box-shadow: none;
}

.join-btn:not(:disabled):active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20rpx 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.toolbar-right {
  display: flex;
  gap: 15rpx;
}

.toolbar-btn {
  background: #f8f9fa;
  color: #333;
  border: 1rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  line-height: 1;
}

.toolbar-btn.danger {
  background: #FF3B30;
  color: white;
  border-color: #FF3B30;
}

.shares-count {
  font-size: 26rpx;
  color: #666;
}

/* 分享测试列表 */
.shares-section {
  margin-bottom: 20rpx;
}

.shares-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.share-item {
  background: white;
  border-radius: 20rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 20rpx;
  transition: all 0.3s ease;
}

.share-item.expired {
  opacity: 0.7;
  background: #f8f9fa;
}

/* 选择框 */
.share-checkbox {
  display: flex;
  align-items: flex-start;
  padding-top: 5rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background: #007AFF;
  border-color: #007AFF;
}

.checkbox-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 分享信息 */
.share-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.share-status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: bold;
}

.share-status.active {
  background: #e8f5e8;
  color: #34C759;
}

.share-status.expired {
  background: #ffe8e8;
  color: #FF3B30;
}

.share-meta {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

.test-mode {
  background: #007AFF;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.share-id {
  font-size: 24rpx;
  color: #666;
}

/* 多关卡任务进度 */
.level-progress {
  background: #f0f9ff;
  padding: 15rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007AFF;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.progress-text {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 6rpx;
  background: #e0e0e0;
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #00C6FF);
  transition: width 0.3s ease;
}

/* 测试统计 */
.test-stats {
  display: flex;
  justify-content: space-between;
  background: #f8f9fa;
  padding: 15rpx;
  border-radius: 12rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
}

.stat-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

/* 时间信息 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.first-time,
.last-time {
  font-size: 24rpx;
  color: #666;
}

/* 操作按钮 */
.share-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  align-items: flex-end;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
  min-width: 120rpx;
  text-align: center;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.primary:disabled {
  background: #e0e0e0;
  color: #999;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #007AFF;
  border: 1rpx solid #007AFF;
}

/* 空状态 */
.empty-section {
  background: white;
  text-align: center;
  padding: 80rpx 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 加载状态 */
.loading-section {
  background: white;
  text-align: center;
  padding: 80rpx 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.loading-text {
  font-size: 30rpx;
  color: #666;
}

/* 历史记录 */
.history-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.history-header {
  margin-bottom: 20rpx;
}

.history-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.history-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 15rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.history-item:active {
  border-color: #007AFF;
  background: #f0f9ff;
}

.history-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.history-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-type {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.test-score {
  font-size: 26rpx;
  color: #007AFF;
  font-weight: bold;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-time {
  font-size: 24rpx;
  color: #666;
}

.rejoin-icon {
  font-size: 32rpx;
  color: #007AFF;
  margin-left: 20rpx;
}

/* 详情弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.modal-content {
  background: white;
  border-radius: 20rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #666;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
}

.detail-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 26rpx;
  color: #666;
}

.detail-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

/* 关卡详情 */
.level-detail {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.level-score-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.level-score-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 15rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.level-name {
  font-size: 24rpx;
  color: #666;
}

.level-score {
  font-size: 24rpx;
  font-weight: bold;
  color: #007AFF;
} 