const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { shareId } = event;
    
    if (!shareId) {
      return {
        success: false,
        message: '分享ID不能为空'
      };
    }
    
    // 从数据库获取分享测试数据
    const result = await db.collection('shareTests')
      .where({
        shareId: shareId
      })
      .get();
    
    if (!result.data || result.data.length === 0) {
      return {
        success: false,
        message: '分享测试不存在'
      };
    }
    
    let shareTestData = result.data[0];

    // 检查是否过期
    if (shareTestData.expireTime) {
      const now = new Date();
      const expireTime = new Date(shareTestData.expireTime);
      if (now > expireTime) {
        return {
          success: false,
          message: '分享测试已过期'
        };
      }
    }

    // 超级优化：根据存储策略动态加载词汇数据
    if (shareTestData.isOptimizedStorage && !shareTestData.words) {
      try {
        const { storageStrategy, wordSelection, libraryId } = shareTestData;
        console.log('检测到优化存储，策略:', storageStrategy, '词库:', libraryId);

        let loadedWords = [];

        if (storageStrategy === 'index_based' && wordSelection) {
          // 基于索引的超级优化存储
          console.log('使用索引存储策略，词汇数量:', wordSelection.totalWords);

          // 获取完整词库数据
          const wordsResult = await db.collection(libraryId).get();
          const allWords = wordsResult.data || [];

          if (allWords.length === 0) {
            throw new Error('词库数据为空');
          }

          // 根据索引序列重新排序词汇
          const wordIndexes = wordSelection.wordIndexes || [];
          loadedWords = wordIndexes.map(index => allWords[index]).filter(word => word);

          console.log(`索引存储加载成功: ${loadedWords.length}/${wordIndexes.length} 个词汇`);

        } else if (storageStrategy === 'reference' && wordSelection?.selectionType === 'full') {
          // 完整词库引用：加载整个词库
          console.log('加载完整词库:', libraryId);
          const wordsResult = await db.collection(libraryId).get();
          loadedWords = wordsResult.data || [];

        } else if (storageStrategy === 'hybrid' && wordSelection?.selectionType === 'partial') {
          // 部分词汇引用：根据词汇ID加载特定词汇
          console.log('根据ID加载部分词汇:', wordSelection.wordIds?.length, '个');
          const wordIds = wordSelection.wordIds || [];

          if (wordIds.length > 0) {
            // 分批查询（微信云数据库单次查询限制）
            const batchSize = 20;
            const batches = [];

            for (let i = 0; i < wordIds.length; i += batchSize) {
              const batchIds = wordIds.slice(i, i + batchSize);
              const batchResult = await db.collection(libraryId)
                .where({
                  _id: db.command.in(batchIds)
                })
                .get();

              if (batchResult.data) {
                batches.push(...batchResult.data);
              }
            }

            // 按原始顺序排序
            loadedWords = wordIds.map(id =>
              batches.find(word => word._id === id)
            ).filter(word => word); // 过滤掉找不到的词汇
          }
        }

        if (loadedWords.length > 0) {
          shareTestData.words = loadedWords;
          console.log('动态加载词汇成功:', loadedWords.length, '个词汇');

          // 动态生成关卡信息（超级优化：延迟生成）
          if (shareTestData.isMultiLevel && !shareTestData.levels) {
            console.log('标记为延迟生成关卡信息');
            shareTestData.levels = []; // 初始化为空数组，按需生成
          }
        } else {
          console.error('无法加载词汇数据:', { storageStrategy, wordSelection, libraryId });
          return {
            success: false,
            message: '词汇数据不存在或已被删除'
          };
        }
      } catch (loadError) {
        console.error('动态加载词汇数据失败:', loadError);
        return {
          success: false,
          message: '加载词汇数据失败'
        };
      }
    }
    
    // 记录访问者信息
    const currentUser = wxContext.OPENID;
    if (currentUser && currentUser !== shareTestData.createdBy) {
      // 检查是否已经记录过该访问者
      const existingVisitor = shareTestData.visitors.find(v => v.openid === currentUser);
      
      if (!existingVisitor) {
        // 添加新访问者
        await db.collection('shareTests').doc(shareTestData._id).update({
          data: {
            visitors: db.command.push({
              openid: currentUser,
              firstVisitTime: db.serverDate(),
              lastVisitTime: db.serverDate(),
              visitCount: 1,
              testCount: 0,
              bestScore: 0
            })
          }
        });
      } else {
        // 更新现有访问者的访问时间
        const updatedVisitors = shareTestData.visitors.map(v => {
          if (v.openid === currentUser) {
            return {
              ...v,
              lastVisitTime: new Date(),
              visitCount: (v.visitCount || 0) + 1
            };
          }
          return v;
        });
        
        await db.collection('shareTests').doc(shareTestData._id).update({
          data: {
            visitors: updatedVisitors
          }
        });
      }
    }

    // 按需生成关卡数据（优化：只在需要时生成，避免一次性生成大量关卡）
    if (shareTestData.needsLazyLevelGeneration && shareTestData.words && shareTestData.words.length > 0) {
      console.log('检测到需要延迟生成关卡，开始按需生成...');

      // 获取当前用户的进度，确定需要生成哪些关卡
      const currentUserProgress = shareTestData.levelProgress ? shareTestData.levelProgress[currentUser] : null;
      const currentLevel = currentUserProgress ? currentUserProgress.currentLevel : 1;
      const wordsPerGroup = shareTestData.wordsPerGroup || 20;

      // 只生成当前关卡和下一关卡的数据（最多生成2个关卡，避免超时）
      const levelsToGenerate = Math.min(2, shareTestData.totalLevels - currentLevel + 1);
      const startLevel = currentLevel;
      const endLevel = Math.min(startLevel + levelsToGenerate - 1, shareTestData.totalLevels);

      console.log(`生成关卡 ${startLevel} 到 ${endLevel}，共 ${endLevel - startLevel + 1} 个关卡`);

      if (!shareTestData.levels) {
        shareTestData.levels = [];
      }

      for (let levelId = startLevel; levelId <= endLevel; levelId++) {
        // 检查是否已经生成过这个关卡
        const existingLevel = shareTestData.levels.find(l => l.levelId === levelId);
        if (!existingLevel) {
          const start = (levelId - 1) * wordsPerGroup;
          const end = Math.min(start + wordsPerGroup, shareTestData.words.length);

          shareTestData.levels.push({
            levelId: levelId,
            words: shareTestData.words.slice(start, end),
            isUnlocked: levelId <= currentLevel
          });
        }
      }

      console.log(`成功生成 ${levelsToGenerate} 个关卡数据`);
    }

    // 返回测试数据（包含访问者和结果信息）
    return {
      success: true,
      data: {
        shareId: shareTestData.shareId,
        testType: shareTestData.testType,
        words: shareTestData.words,
        libraryId: shareTestData.libraryId,
        libraryName: shareTestData.libraryName,
        settings: shareTestData.settings,
        createdBy: shareTestData.createdBy,
        createTime: shareTestData.createTime,
        expireTime: shareTestData.expireTime,
        expireDays: shareTestData.expireDays,
        isMultiLevel: shareTestData.isMultiLevel,
        totalLevels: shareTestData.totalLevels,
        wordsPerGroup: shareTestData.wordsPerGroup,
        levels: shareTestData.levels,
        // 返回访问者和结果信息（创建者可以查看）
        visitors: shareTestData.visitors || [],
        results: shareTestData.results || [],
        levelProgress: shareTestData.levelProgress || {},
        // 返回当前用户的进度信息
        currentUserProgress: shareTestData.levelProgress ? shareTestData.levelProgress[currentUser] : null,
        // 统计信息
        participantCount: shareTestData.visitors ? shareTestData.visitors.length : 0,
        testResultCount: shareTestData.results ? shareTestData.results.length : 0
      }
    };
    
  } catch (error) {
    console.error('获取分享测试失败:', error);
    return {
      success: false,
      message: '获取分享测试失败',
      error: error
    };
  }
}; 