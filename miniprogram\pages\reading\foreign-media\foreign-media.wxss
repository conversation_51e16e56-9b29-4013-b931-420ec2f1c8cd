/* 外刊阅读页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 类别选择器 */
.category-selector {
  margin-bottom: 40rpx;
}

.selector-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 12rpx;
  border-left: 8rpx solid #F59E0B;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 4rpx;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 120rpx;
}

.category-item.active {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  color: white;
  transform: scale(1.05);
}

.category-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.category-name {
  font-size: 24rpx;
  font-weight: 500;
}

/* 文章容器 */
.articles-container {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 12rpx;
  border-left: 8rpx solid #F59E0B;
}

.articles-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.article-card {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.article-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.article-header {
  margin-bottom: 16rpx;
}

.article-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.article-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.meta-item {
  margin-right: 8rpx;
}

.meta-divider {
  margin: 0 8rpx;
  color: #ccc;
}

.article-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-tags {
  display: flex;
  gap: 12rpx;
}

.difficulty-tag {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 2rpx solid rgba(245, 158, 11, 0.3);
}

.time-tag {
  background: rgba(59, 130, 246, 0.1);
  color: #3B82F6;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.3);
}

.read-btn {
  display: flex;
  align-items: center;
  color: #F59E0B;
  font-size: 28rpx;
  font-weight: 500;
}

.arrow {
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

.article-card:active .arrow {
  transform: translateX(4rpx);
}

/* 阅读指南 */
.reading-guide {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-item {
  display: flex;
  align-items: flex-start;
}

.guide-dot {
  color: #F59E0B;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.guide-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
} 