// pages/profile/received/received.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    receivedShares: [], // 收到的分享列表
    loading: true,
    currentUser: null,
    showDetailModal: false,
    currentDetail: null,
    statistics: {
      totalShares: 0,
      totalTests: 0,
      averageScore: 0,
      recentActivity: null
    },
    shareId: '', // 输入的分享ID
    recentShares: [], // 最近参与的分享
    showHistory: false, // 是否显示历史记录
    allSelected: false // 是否全选
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getCurrentUser();
    this.loadReceivedShares();
    this.loadRecentShares();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示页面时重新加载数据
    this.loadReceivedShares();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadReceivedShares();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    const userInfo = wx.getStorageSync('userInfo') || {};
    this.setData({
      currentUser: userInfo
    });
  },

  /**
   * 加载收到的分享记录
   */
  async loadReceivedShares() {
    try {
      this.setData({ loading: true });
      
      const currentUser = this.data.currentUser;
      if (!currentUser || !currentUser.openid) {
        this.setData({ 
          loading: false,
          receivedShares: [] 
        });
        return;
      }

      // 首先尝试从云端获取参与的分享测试
      let cloudShares = [];
      try {
        const result = await wx.cloud.callFunction({
          name: 'getMyShares',
          data: {
            type: 'participated'
          }
        });
        
        if (result.result.success) {
          cloudShares = result.result.data;
          console.log('从云端加载参与的分享测试:', cloudShares.length);
        }
      } catch (cloudError) {
        console.log('从云端加载分享测试失败，使用本地数据:', cloudError);
      }

      // 同时从本地存储获取分享测试（兼容旧数据）
      const shareTests = wx.getStorageSync('shareTests') || {};
      const localShares = [];

      // 遍历所有分享测试，找到当前用户参与的
      Object.keys(shareTests).forEach(shareId => {
        const shareData = shareTests[shareId];
        
        // 检查当前用户是否访问过这个分享
        if (shareData.visitors) {
          const visitor = shareData.visitors.find(v => v.openid === currentUser.openid);
          if (visitor) {
            // 获取当前用户在这个分享中的测试记录
            const userResults = shareData.results ? 
              shareData.results.filter(r => r.participantOpenid === currentUser.openid) : [];

            localShares.push({
              shareId: shareId,
              shareData: shareData,
              visitor: visitor,
              userResults: userResults,
              shareTitle: this.generateShareTitle(shareData),
              testMode: shareData.testMode || shareData.testType,
              creatorInfo: shareData.creatorInfo,
              createTime: shareData.createTime,
              lastTestTime: visitor.lastTestTime,
              testCount: visitor.testCount || 0,
              bestScore: visitor.bestScore || 0,
              averageScore: visitor.averageScore || 0,
              isExpired: this.checkIfExpired(shareData),
              isMultiLevel: shareData.isMultiLevel || false,
              totalLevels: shareData.totalLevels || 1,
              myProgress: shareData.levelProgress ? shareData.levelProgress[currentUser.openid] : null
            });
          }
        }
      });

      // 合并云端和本地数据，去重
      const allShares = [...cloudShares];
      const cloudShareIds = new Set(cloudShares.map(share => share.shareId));
      
      localShares.forEach(localShare => {
        if (!cloudShareIds.has(localShare.shareId)) {
          allShares.push({
            ...localShare,
            myInfo: {
              firstVisitTime: localShare.visitor?.firstVisitTime,
              lastTestTime: localShare.lastTestTime,
              testCount: localShare.testCount,
              bestScore: localShare.bestScore,
              latestScore: localShare.visitor?.latestScore || 0,
              latestAccuracy: localShare.visitor?.latestAccuracy || 0,
              results: localShare.userResults,
              progress: localShare.myProgress
            }
          });
        }
      });

      // 按最后测试时间排序
      allShares.sort((a, b) => {
        const timeA = a.myInfo?.lastTestTime || a.lastTestTime || a.createTime || 0;
        const timeB = b.myInfo?.lastTestTime || b.lastTestTime || b.createTime || 0;
        return new Date(timeB) - new Date(timeA);
      });

              // 为每个分享添加选中状态（用于批量删除）
        const receivedShares = allShares.map((share, index) => ({
          ...share,
          index: index,
                    isSelected: false, // 选中状态
          // 格式化时间显示
          firstVisitTimeText: this.formatTime(share.myInfo?.firstVisitTime || share.firstVisitTime),
          lastTestTimeText: this.formatTime(share.myInfo?.lastTestTime || share.lastTestTime),
          // 多关卡进度显示
          levelProgressText: this.getLevelProgressText(share),
        shareTitle: share.shareTitle || this.generateShareTitle(share),
        isExpired: share.isExpired || this.checkIfExpired(share)
      }));

      // 计算统计数据
      const statistics = this.calculateStatistics(receivedShares);

      this.setData({
        receivedShares: receivedShares,
        statistics: statistics,
        loading: false,
        allSelected: false // 初始化全选状态
      });

    } catch (error) {
      console.error('加载收到的分享失败:', error);
      this.setData({ 
        loading: false,
        receivedShares: [] 
      });
    }
  },

  /**
   * 生成分享标题
   */
  generateShareTitle(shareData) {
    const testModeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写测试',
      'custom': '消消乐',
      'elimination': '消消乐'
    };
    
    const mode = testModeMap[shareData.testMode || shareData.testType] || '测试';
    const library = shareData.libraryName || '词库';
    
    return `${mode} - ${library}`;
  },

  /**
   * 检查分享是否过期
   */
  checkIfExpired(shareData) {
    if (!shareData.expireTime) {
      return false;
    }
    
    const now = new Date();
    const expireTime = new Date(shareData.expireTime);
    return now > expireTime;
  },

  /**
   * 计算统计数据
   */
  calculateStatistics(receivedShares) {
    if (receivedShares.length === 0) {
      return {
        totalShares: 0,
        totalTests: 0,
        averageScore: 0,
        recentActivity: null
      };
    }

    const totalShares = receivedShares.length;
    const totalTests = receivedShares.reduce((sum, share) => sum + share.testCount, 0);
    
    // 计算平均分（只考虑有测试记录的分享）
    const scoresSum = receivedShares.reduce((sum, share) => {
      return sum + (share.averageScore || 0) * share.testCount;
    }, 0);
    const averageScore = totalTests > 0 ? Math.round(scoresSum / totalTests) : 0;

    // 最近活动
    const recentShare = receivedShares.find(share => share.lastTestTime);
    const recentActivity = recentShare ? recentShare.lastTestTime : null;

    return {
      totalShares,
      totalTests,
      averageScore,
      recentActivity
    };
  },

  /**
   * 查看分享详情
   */
  viewShareDetail(e) {
    const index = e.currentTarget.dataset.index;
    const share = this.data.receivedShares[index];
    
    // 计算平均分（避免在WXML中使用复杂表达式）
    let averageScore = 0;
    if (share.myInfo && share.myInfo.results && share.myInfo.results.length > 0) {
      const totalScore = share.myInfo.results.reduce((sum, result) => sum + result.score, 0);
      averageScore = Math.round(totalScore / share.myInfo.results.length);
    }
    
    // 为详情弹窗准备数据
    const detailData = {
      ...share,
      averageScore: averageScore
    };
    
    this.setData({
      currentDetail: detailData,
      showDetailModal: true
    });
  },

  /**
   * 关闭详情弹窗
   */
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      currentDetail: null
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  },

  /**
   * 重新参加测试
   */
  retakeTest(e) {
    const index = e.currentTarget.dataset.index;
    const share = this.data.receivedShares[index];
    
    // 根据测试类型跳转到对应页面
    let url = '';
    switch (share.testMode) {
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${share.shareId}&shareMode=share&testMode=${share.testMode}`;
        break;
      case 'dictation':
        url = `/pages/spelling/practice/practice?shareId=${share.shareId}&shareMode=share`;
        break;
      case 'custom':
        url = `/pages/task/puzzle/puzzle?shareId=${share.shareId}&isShared=true&mode=custom`;
        break;
      default:
        wx.showToast({
          title: '未知的测试类型',
          icon: 'none'
        });
        return;
    }
    
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 查看我的测试记录
   */
  viewMyResults(e) {
    const index = e.currentTarget.dataset.index;
    const share = this.data.receivedShares[index];
    
    if (share.userResults.length === 0) {
      wx.showToast({
        title: '暂无测试记录',
        icon: 'none'
      });
      return;
    }

    // 跳转到测试记录详情页面（可以复用现有的用户报告页面）
    wx.navigateTo({
      url: `/pages/profile/share/user-report/user-report?userId=${this.data.currentUser.openid}&shareId=${share.shareId}`
    });
  },

  /**
   * 删除分享记录
   */
  deleteShare(e) {
    const index = e.currentTarget.dataset.index;
    const share = this.data.receivedShares[index];
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除分享"${share.shareTitle}"吗？\n删除后将无法再查看此分享的测试记录。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteShare(share.shareId);
        }
      }
    });
  },

  /**
   * 执行删除操作
   */
  doDeleteShare(shareId) {
    try {
      const shareTests = wx.getStorageSync('shareTests') || {};
      const shareData = shareTests[shareId];
      
      if (shareData && shareData.visitors) {
        // 从访问者列表中移除当前用户
        shareData.visitors = shareData.visitors.filter(v => v.openid !== this.data.currentUser.openid);
        
        // 从测试结果中移除当前用户的记录
        if (shareData.results) {
          shareData.results = shareData.results.filter(r => r.participantOpenid !== this.data.currentUser.openid);
        }
        
        shareTests[shareId] = shareData;
        wx.setStorageSync('shareTests', shareTests);
      }
      
      // 重新加载数据
      this.loadReceivedShares();
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });
      
    } catch (error) {
      console.error('删除分享失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      // 今天
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays === 1) {
      return '昨天';
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  },

  /**
   * 格式化测试模式
   */
  getTestModeText(mode) {
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写测试',
      'custom': '消消乐'
    };
    return modeMap[mode] || '未知模式';
  },

  /**
   * 加载最近参与的分享
   */
  loadRecentShares() {
    try {
      const testResults = wx.getStorageSync('testResults') || [];
      const recentShares = testResults
        .filter(result => result.shareMode === 'share' && result.shareId)
        .slice(-10) // 只显示最近10个
        .reverse(); // 最新的在前面
      
      this.setData({ 
        recentShares,
        showHistory: recentShares.length > 0
      });
    } catch (error) {
      console.error('加载历史记录失败:', error);
    }
  },

  /**
   * 输入分享ID
   */
  onShareIdInput(e) {
    this.setData({
      shareId: e.detail.value.trim()
    });
  },

  /**
   * 参与分享测试
   */
  async joinShareTest() {
    const { shareId } = this.data;
    
    if (!shareId) {
      wx.showToast({
        title: '请输入测试ID',
        icon: 'none'
      });
      return;
    }

    this.setData({ loading: true });

    try {
      // 首先尝试从云端获取分享测试
      let shareTestData = null;
      
      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: {
            shareId: shareId
          }
        });
        
        if (result.result.success) {
          shareTestData = result.result.data;
          console.log('从云端获取分享测试成功:', shareTestData);
        } else {
          throw new Error(result.result.message || '获取分享测试失败');
        }
      } catch (cloudError) {
        console.log('从云端获取分享测试失败，尝试本地存储:', cloudError);
        
        // 如果云端获取失败，尝试本地存储
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareTestData = shareTests[shareId];
        
        if (shareTestData) {
          console.log('从本地存储获取分享测试成功');
        }
      }

      this.setData({ loading: false });

      if (shareTestData) {
        // 检查是否过期
        if (this.checkIfExpired(shareTestData)) {
          wx.showModal({
            title: '测试已过期',
            content: '该分享测试已过期，无法参与。',
            showCancel: false,
            confirmText: '知道了'
          });
          return;
        }
        
        this.navigateToTest(shareTestData);
      } else {
        wx.showModal({
          title: '测试不存在',
          content: '找不到该测试ID对应的分享测试。\n\n请确认：\n1. 测试ID输入正确\n2. 测试未过期\n3. 分享链接有效',
          showCancel: false,
          confirmText: '知道了'
        });
      }
    } catch (error) {
      this.setData({ loading: false });
      console.error('参与分享测试失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 跳转到对应的测试页面
   */
  navigateToTest(shareTestData) {
    const { shareId, testMode } = shareTestData;
    let url = '';

    switch (testMode) {
      case 'dictation':
        url = `/pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
        break;
      case 'en_to_cn':
      case 'cn_to_en':
        url = `/pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
        break;
      case 'elimination':
        url = `/pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom`;
        break;
      case 'custom':
        url = `/pages/task/custom/custom?shareId=${shareId}&shareMode=share`;
        break;
      default:
        wx.showToast({
          title: '不支持的测试类型',
          icon: 'none'
        });
        return;
    }

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('跳转到分享测试:', url);
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 重新参与历史测试
   */
  rejoinTest(e) {
    const { index } = e.currentTarget.dataset;
    const shareResult = this.data.recentShares[index];
    
    if (shareResult && shareResult.shareId) {
      this.setData({ shareId: shareResult.shareId });
      this.joinShareTest();
    }
  },

  /**
   * 清空输入
   */
  clearInput() {
    this.setData({ shareId: '' });
  },

  /**
   * 扫码参与（预留功能）
   */

  /**
   * 获取关卡进度文本
   */
  getLevelProgressText(share) {
    if (!share.isMultiLevel) {
      return null;
    }
    
    const progress = share.myInfo?.progress || share.myProgress;
    if (progress) {
      const completed = progress.completedLevels ? progress.completedLevels.length : 0;
      const total = share.totalLevels || 1;
      const current = progress.currentLevel || 1;
      
      return `第${current}关 | 完成${completed}/${total}关`;
    }
    
    return `第1关 | 完成0/${share.totalLevels || 1}关`;
  },

  /**
   * 选择/取消选择分享
   */
  toggleSelectShare(e) {
    const index = e.currentTarget.dataset.index;
    const receivedShares = this.data.receivedShares;
    receivedShares[index].isSelected = !receivedShares[index].isSelected;
    
    // 检查是否全选
    const allSelected = receivedShares.every(share => share.isSelected);
    
    this.setData({
      receivedShares: receivedShares,
      allSelected: allSelected
    });
  },

  /**
   * 全选/取消全选
   */
  toggleSelectAll() {
    const receivedShares = this.data.receivedShares;
    const currentAllSelected = this.data.allSelected;
    
    receivedShares.forEach(share => {
      share.isSelected = !currentAllSelected;
    });
    
    this.setData({
      receivedShares: receivedShares,
      allSelected: !currentAllSelected
    });
  },

  /**
   * 批量删除选中的分享
   */
  batchDeleteShares() {
    const selectedShares = this.data.receivedShares.filter(share => share.isSelected);
    
    if (selectedShares.length === 0) {
      wx.showToast({
        title: '请选择要删除的分享',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${selectedShares.length}个分享吗？`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.doBatchDelete(selectedShares);
        }
      }
    });
  },

  /**
   * 执行批量删除
   */
  async doBatchDelete(selectedShares) {
    try {
      wx.showLoading({
        title: '删除中...',
        mask: true
      });
      
      const deletePromises = selectedShares.map(share => {
        return wx.cloud.callFunction({
          name: 'deleteShareTest',
          data: {
            shareId: share.shareId,
            deleteType: 'participant' // 参与者删除
          }
        });
      });
      
      const results = await Promise.allSettled(deletePromises);
      
      // 检查删除结果
      const successCount = results.filter(result => 
        result.status === 'fulfilled' && result.value?.result?.success
      ).length;
      
      wx.hideLoading();
      
      if (successCount > 0) {
        wx.showToast({
          title: `成功删除${successCount}个分享`,
          icon: 'success'
        });
        
        // 重新加载数据
        setTimeout(() => {
          this.loadReceivedShares();
        }, 1000);
      } else {
        wx.showToast({
          title: '删除失败，请重试',
          icon: 'error'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 查看分享管理页面
   */
  viewShareManagement(e) {
    const shareId = e.currentTarget.dataset.shareid;
    wx.navigateTo({
      url: `/pages/profile/share-management/share-management?shareId=${shareId}`
    });
  }

})