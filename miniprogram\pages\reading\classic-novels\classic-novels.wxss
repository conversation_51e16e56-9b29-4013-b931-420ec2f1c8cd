/* 英语名著小说页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 小说容器 */
.novels-container {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  padding-left: 12rpx;
  border-left: 8rpx solid #8B5CF6;
}

.novels-grid {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.novel-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.novel-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.card-content {
  background: linear-gradient(135deg, var(--novel-color-1), var(--novel-color-2));
  color: white;
  padding: 0;
}

/* 小说颜色主题 */
.card-content.purple {
  --novel-color-1: #8B5CF6;
  --novel-color-2: #7C3AED;
}

.card-content.pink {
  --novel-color-1: #EC4899;
  --novel-color-2: #DB2777;
}

.card-content.gold {
  --novel-color-1: #F59E0B;
  --novel-color-2: #D97706;
}

.card-content.blue {
  --novel-color-1: #3B82F6;
  --novel-color-2: #2563EB;
}

.card-content.green {
  --novel-color-1: #10B981;
  --novel-color-2: #059669;
}

.card-content.orange {
  --novel-color-1: #F97316;
  --novel-color-2: #EA580C;
}

.novel-header {
  padding: 32rpx 24rpx 16rpx;
  background: rgba(0, 0, 0, 0.1);
}

.novel-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.english-title {
  font-size: 28rpx;
  opacity: 0.9;
  margin-bottom: 12rpx;
  font-style: italic;
}

.author {
  font-size: 24rpx;
  opacity: 0.8;
}

.novel-info {
  padding: 20rpx 24rpx;
}

.info-tags {
  display: flex;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.difficulty-tag, .time-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.description {
  font-size: 26rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.card-footer {
  padding: 0 24rpx 24rpx;
}

.read-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.arrow {
  margin-left: 8rpx;
  transition: transform 0.3s ease;
}

.novel-card:active .arrow {
  transform: translateX(4rpx);
}

/* 阅读指南 */
.reading-guide {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
}

.guide-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.guide-item {
  display: flex;
  align-items: flex-start;
}

.guide-dot {
  color: #8B5CF6;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.guide-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 开发提示 */
.development-notice {
  background: #e3f2fd;
  border: 2rpx solid #bbdefb;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-weight: bold;
  color: #1565c0;
  font-size: 28rpx;
}

.notice-content {
  color: #1565c0;
  font-size: 26rpx;
} 