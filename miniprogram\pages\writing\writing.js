Page({
  data: {
    modules: [
      {
        id: 'topic_materials',
        title: '话题素材',
        subtitle: '丰富写作素材库',
        description: '精选优质写作素材，覆盖各类话题',
        icon: '📚',
        color: 'blue'
      },
      {
        id: 'topic_essays',
        title: '话题范文',
        subtitle: '经典范文赏析',
        description: '优秀作文范例，提升写作水平',
        icon: '✨',
        color: 'purple'
      }
    ],
    topics: [
      '文化习俗/跨文化交流',
      '青少年校园生活/社会生活/成长与选择',
      '教育学习',
      '科技媒体',
      '环境保护',
      '体育健康',
      '艺术',
      '人际关系',
      '公益活动',
      '旅行',
      '文学',
      '职业/大学专业'
    ]
  },

  onLoad() {
    wx.setNavigationBarTitle({
      title: '写作积累'
    });
  },

  // 选择模块
  onModuleSelect(e) {
    const moduleId = e.currentTarget.dataset.id;
    const selectedModule = this.data.modules.find(module => module.id === moduleId);
    
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 跳转到话题选择页面
    wx.navigateTo({
      url: `/pages/writing/topics/topics?moduleId=${moduleId}&moduleTitle=${selectedModule.title}`
    });
  }
}); 