const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext();
  
  try {
    const { type = 'created' } = event; // 'created' 或 'participated'
    const currentUser = wxContext.OPENID;
    
    if (type === 'created') {
      // 获取我创建的分享测试
      const result = await db.collection('shareTests')
        .where({
          createdBy: currentUser
        })
        .orderBy('createTime', 'desc')
        .get();
      
      const shareTests = result.data.map(test => {
        const now = new Date();
        const expireTime = new Date(test.expireTime);
        const isExpired = now > expireTime;
        
        return {
          ...test,
          isExpired: isExpired,
          participantCount: test.visitors ? test.visitors.length : 0,
          testResultCount: test.results ? test.results.length : 0,
          // 计算统计信息
          stats: {
            totalTests: test.results ? test.results.length : 0,
            averageScore: test.results && test.results.length > 0 
              ? Math.round(test.results.reduce((sum, r) => sum + r.score, 0) / test.results.length)
              : 0,
            highestScore: test.results && test.results.length > 0
              ? Math.max(...test.results.map(r => r.score))
              : 0,
            recentActivity: test.results && test.results.length > 0
              ? test.results[test.results.length - 1].submitTime
              : null
          }
        };
      });
      
      return {
        success: true,
        data: shareTests
      };
      
    } else if (type === 'participated') {
      // 获取我参与的分享测试
      const result = await db.collection('shareTests')
        .where({
          'visitors.openid': currentUser
        })
        .orderBy('createTime', 'desc')
        .get();
      
      const participatedTests = result.data.map(test => {
        const now = new Date();
        const expireTime = new Date(test.expireTime);
        const isExpired = now > expireTime;
        
        // 找到当前用户的访问记录
        const myVisitorInfo = test.visitors.find(v => v.openid === currentUser);
        const myResults = test.results ? test.results.filter(r => r.participantOpenid === currentUser) : [];
        const myProgress = test.levelProgress ? test.levelProgress[currentUser] : null;
        
        return {
          shareId: test.shareId,
          testType: test.testType,
          libraryName: test.libraryName,
          createdBy: test.createdBy,
          createTime: test.createTime,
          expireTime: test.expireTime,
          isExpired: isExpired,
          isMultiLevel: test.isMultiLevel,
          totalLevels: test.totalLevels,
          // 我的参与信息
          myInfo: {
            firstVisitTime: myVisitorInfo ? myVisitorInfo.firstVisitTime : null,
            lastTestTime: myVisitorInfo ? myVisitorInfo.lastTestTime : null,
            testCount: myVisitorInfo ? myVisitorInfo.testCount : 0,
            bestScore: myVisitorInfo ? myVisitorInfo.bestScore : 0,
            latestScore: myVisitorInfo ? myVisitorInfo.latestScore : 0,
            latestAccuracy: myVisitorInfo ? myVisitorInfo.latestAccuracy : 0,
            results: myResults,
            progress: myProgress
          }
        };
      });
      
      return {
        success: true,
        data: participatedTests
      };
    }
    
    return {
      success: false,
      message: '无效的查询类型'
    };
    
  } catch (error) {
    console.error('获取分享测试列表失败:', error);
    return {
      success: false,
      message: '获取分享测试列表失败',
      error: error
    };
  }
}; 