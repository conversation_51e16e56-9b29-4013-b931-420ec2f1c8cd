<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">{{moduleTitle}}</view>
    <view class="subtitle">选择您感兴趣的话题</view>
  </view>

  <!-- 话题列表 -->
  <view class="topics-container">
    <view class="topics-grid">
      <view 
        class="topic-card" 
        wx:for="{{topics}}" 
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onTopicSelect"
      >
        <view class="card-content {{item.color}}">
          <view class="topic-icon">{{item.icon}}</view>
          <view class="topic-info">
            <view class="topic-title">{{item.title}}</view>
            <view class="topic-description">{{item.description}}</view>
          </view>
          <view class="arrow-icon">→</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 开发提示 -->
  <view class="development-notice">
    <view class="notice-icon">🚧</view>
    <view class="notice-text">
      <text class="notice-title">注意：</text>
      <text class="notice-content">这只是示例内容，不是正式内容，具体内容正在完善中</text>
    </view>
  </view>
</view> 