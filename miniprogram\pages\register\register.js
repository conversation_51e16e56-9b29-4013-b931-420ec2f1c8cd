const app = getApp()

Page({
  data: {
    username: '',
    password: '',
    confirmPassword: ''
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    })
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 确认密码输入
  onConfirmPasswordInput(e) {
    this.setData({
      confirmPassword: e.detail.value
    })
  },

  // 处理注册
  async handleRegister() {
    const { username, password, confirmPassword } = this.data

    // 表单验证
    if (!username || !password || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    if (username.length < 4 || username.length > 20) {
      wx.showToast({
        title: '用户名长度为4-20个字符',
        icon: 'none'
      })
      return
    }

    if (password.length < 6 || password.length > 20) {
      wx.showToast({
        title: '密码长度为6-20个字符',
        icon: 'none'
      })
      return
    }

    if (password !== confirmPassword) {
      wx.showToast({
        title: '两次输入的密码不一致',
        icon: 'none'
      })
      return
    }

    try {
      wx.showLoading({
        title: '注册中...'
      })

      const res = await wx.cloud.callFunction({
        name: 'register',
        data: {
          username,
          password
        }
      })

      if (res.result.code === 200) {
        // 保存登录信息
        wx.setStorageSync('token', res.result.data.token)
        wx.setStorageSync('userInfo', res.result.data.userInfo)

        wx.showToast({
          title: '注册成功',
          icon: 'success'
        })

        // 跳转到首页
        wx.redirectTo({
          url: '/pages/index/index'
        })
      } else {
        wx.showToast({
          title: res.result.message,
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('注册失败：', error)
      wx.showToast({
        title: '注册失败，请重试',
        icon: 'none'
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 返回登录页
  handleBack() {
    wx.navigateBack()
  },

  // 显示用户协议
  showAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/agreement'
    })
  },

  // 显示隐私政策
  showPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy'
    })
  }
}) 