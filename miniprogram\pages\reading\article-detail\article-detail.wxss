/* 文章详情页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 文章头部 */
.article-header {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

.article-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.meta-source {
  color: #4F46E5;
  font-weight: 500;
}

.meta-divider {
  margin: 0 12rpx;
  color: #ccc;
}

.meta-date {
  color: #666;
}

.article-author {
  font-size: 26rpx;
  color: #666;
}

.author-label {
  opacity: 0.8;
}

.author-name {
  color: #4F46E5;
  font-weight: 500;
}

/* 控制按钮 */
.controls {
  margin-bottom: 24rpx;
  display: flex;
  justify-content: center;
}

.control-btn {
  background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
  color: white;
  border: none;
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(79, 70, 229, 0.3);
  transition: all 0.3s ease;
}

.control-btn:active {
  transform: scale(0.95);
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 文章内容 */
.article-content {
  margin-bottom: 40rpx;
}

.paragraph-container {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  display: flex;
  transition: all 0.3s ease;
  cursor: pointer;
}

.paragraph-container:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.paragraph-number {
  background: linear-gradient(135deg, #4F46E5 0%, #3B82F6 100%);
  color: white;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.paragraph-content {
  flex: 1;
}

.english-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
  margin-bottom: 16rpx;
}

.chinese-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
  padding: 16rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border-left: 6rpx solid #4F46E5;
  transition: all 0.3s ease;
  margin-top: 12rpx;
}

.chinese-text.hide {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
  overflow: hidden;
}

.chinese-text.show {
  opacity: 1;
  height: auto;
}

.tap-hint {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  padding: 8rpx;
  background: #f1f5f9;
  border-radius: 8rpx;
  margin-top: 12rpx;
  border: 2rpx dashed #cbd5e1;
}

/* 阅读提示 */
.reading-tips {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 40rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: flex-start;
}

.tip-dot {
  color: #4F46E5;
  font-weight: bold;
  margin-right: 12rpx;
  margin-top: 4rpx;
}

.tip-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .paragraph-container {
    flex-direction: column;
  }
  
  .paragraph-number {
    align-self: flex-start;
    margin-bottom: 16rpx;
    margin-right: 0;
  }
} 