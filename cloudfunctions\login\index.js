// 云函数入口文件
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 生成token
function generateToken() {
  return crypto.randomBytes(32).toString('hex')
}

// 密码加密
function encryptPassword(password) {
  if (typeof password !== 'string' || !password) {
    throw new Error('密码不能为空');
  }
  return crypto.createHash('sha256').update(password).digest('hex')
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { username, password, code } = event

  // 如果提供了code，说明是微信登录，直接返回openid
  if (code) {
    try {
      const wxContext = cloud.getWXContext()
      const openid = wxContext.OPENID
      
      if (!openid) {
        return {
          success: false,
          message: '获取用户标识失败'
        }
      }

      return {
        success: true,
        message: '获取用户标识成功',
        data: {
          openid: openid
        }
      }
    } catch (error) {
      console.error('获取openid失败:', error)
      return {
        success: false,
        message: '获取用户标识失败',
        error: error.message
      }
    }
  }

  // 原有的用户名密码登录逻辑
  if (!username || !password) {
    return {
      code: 400,
      message: '用户名或密码不能为空'
    }
  }

  try {
    // 查询用户
    const user = await usersCollection.where({
      username,
      password: encryptPassword(password)
    }).get()

    if (user.data.length === 0) {
      return {
        code: 401,
        message: '用户名或密码错误'
      }
    }

    // 生成新token
    const token = generateToken()
    const now = new Date()

    // 更新用户token和登录时间
    await usersCollection.doc(user.data[0]._id).update({
      data: {
        token,
        lastLoginTime: now
      }
    })

    // 为gengruihuan账号设置VIP状态
    let membershipInfo = user.data[0].membership || {
      isVip: false,
      expireTime: null,
      type: 'free'
    }
    
    if (username === 'gengruihuan') {
      membershipInfo = {
        isVip: true,
        expireTime: '2025-12-31',
        type: 'vip'
      }
      
      // 更新数据库中的会员信息
      await usersCollection.doc(user.data[0]._id).update({
        data: {
          membership: membershipInfo
        }
      })
    }

    return {
      code: 200,
      message: '登录成功',
      data: {
        token,
        userInfo: {
          _id: user.data[0]._id,
          username: user.data[0].username,
          openid: user.data[0].openid,
          settings: user.data[0].settings,
          stats: user.data[0].stats,
          membership: membershipInfo
        }
      }
    }
  } catch (e) {
    console.error(e)
    return {
      code: 500,
      message: '服务器错误',
      error: e
    }
  }
} 