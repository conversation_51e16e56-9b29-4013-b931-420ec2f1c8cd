const { LIBRARY_INFO } = require('../../../config/constants.js');
const VIP_CONFIG = require('../../../utils/vip-config.js');

Page({
  data: {
    libraryId: '',
    libraryName: '',
    mode: 'select', // select选择模式, test测试模式
    presetTestMode: '', // 预设的测试模式：en_to_cn, cn_to_en, elimination
    words: [],
    selectedWords: [],
    loading: false,
    searchKeyword: '',
    showVipLimit: false,
    showVipFeatures: false, // 是否显示VIP相关功能
    wordsPerGroup: 10, // 默认每组10个词汇
    wordsPerGroupOptions: [], // 可选的每组词汇数量
    unit: '', // 单元ID
    unitName: '', // 单元名称
    
    // 翻页相关
    currentPage: 1,
    pageSize: 50,
    totalPages: 1,
    totalWords: 0,
    hasMore: true,
    hasPrev: false
  },

  onLoad: function(options) {
    const { libraryId, mode, testMode, shareMode, targetMode, createCompetition, unit, unitName, isPhrase } = options;
    if (libraryId) {
      const libraryInfo = LIBRARY_INFO[libraryId];
      
      // 初始化每组词汇数量选项
      const maxWords = this.calculateMaxWords();
      const wordsPerGroupOptions = this.generateWordsPerGroupOptions(maxWords);
      const defaultWordsPerGroup = Math.min(10, maxWords); // 默认10个或最大值
      
      this.setData({
        libraryId,
        libraryName: libraryInfo ? libraryInfo.name : '未知词库',
        mode: mode || 'select', // select为选择模式，test为测试模式，continue为继续学习模式
        presetTestMode: testMode || '', // 预设的测试模式
        shareMode: shareMode || '', // 分享模式：create为创建分享测试
        targetMode: targetMode || '', // 来自学习进度页面的目标模式
        createCompetition: createCompetition === 'true', // 是否为创建竞赛模式
        isPhrase: isPhrase === 'true', // 是否为短语模式
        showVipFeatures: VIP_CONFIG.enabled, // 根据配置决定是否显示VIP功能
        wordsPerGroup: defaultWordsPerGroup,
        wordsPerGroupOptions,
        unit: unit || '', // 单元ID
        unitName: unitName || '' // 单元名称
      });
      
      // 设置页面标题
      if (unitName) {
        wx.setNavigationBarTitle({
          title: `${libraryInfo ? libraryInfo.name : '词库'} - ${unitName}`
        });
      } else {
        wx.setNavigationBarTitle({
          title: libraryInfo ? libraryInfo.name : '词库'
        });
      }
      
      this.loadWords();
      
      // 如果是继续学习模式，设置标记，在数据加载完成后开始学习
      if (mode === 'continue' && targetMode) {
        this.setData({
          pendingContinueMode: targetMode
        });
      }
    }
  },

  // 加载词汇列表
  loadWords: function(page = 1) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    const { libraryId, pageSize, searchKeyword, unit } = this.data;
    const skip = (page - 1) * pageSize;
    
    console.log(`=== 加载第${page}页词汇 ===`);
    console.log('跳过:', skip, '每页:', pageSize, '单元:', unit);
    
    wx.cloud.callFunction({
      name: 'getWords',
      data: {
        libraryId,
        skip: skip,
        limit: pageSize,
        keyword: searchKeyword,
        getTotalCount: true, // 获取总数
        unit: unit // 传递单元参数
      }
    }).then(result => {
      if (result.result.code === 200) {
        const { data: newWords, totalCount } = result.result;
        console.log('成功加载词汇:', newWords.length, '个，总数:', totalCount);
        
        // 计算分页信息
        const totalPages = Math.ceil(totalCount / pageSize);
        
        // 为每个词汇添加selected属性
        const wordsWithSelected = newWords.map(word => ({
          ...word,
          selected: this.data.selectedWords.some(w => w._id === word._id)
        }));
        
        this.setData({
          words: wordsWithSelected,
          currentPage: page,
          totalPages: totalPages,
          totalWords: totalCount,
          hasMore: page < totalPages,
          hasPrev: page > 1,
          loading: false
        });

        // 设置VIP限制显示
        this.checkVipLimit();

        // 如果有待处理的继续学习模式，现在执行
        if (this.data.pendingContinueMode) {
          const targetMode = this.data.pendingContinueMode;
          this.setData({ pendingContinueMode: null });

          // 延迟一下确保数据完全设置完成
          setTimeout(() => {
            this.continueStudyWithMode(targetMode);
          }, 100);
        }
      } else {
        console.error('云函数返回错误:', result.result);
        wx.showToast({ 
          title: result.result.message || '加载失败', 
          icon: 'error' 
        });
        this.setData({ loading: false });
      }
    }).catch(error => {
      console.error('调用云函数失败:', error);
      wx.showToast({ 
        title: '网络错误', 
        icon: 'error' 
      });
      this.setData({ loading: false });
    });
  },

  // 检查VIP限制
  checkVipLimit: function() {
    const userInfo = getApp().globalData.userInfo;
    const hasVipAccess = VIP_CONFIG.checkVipAccess(userInfo);
    
    if (!hasVipAccess && this.data.words.length > 100) {
      this.setData({ showVipLimit: true });
    }
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadWords(1);
    wx.stopPullDownRefresh();
  },

  // 上一页
  onPrevPage: function() {
    if (this.data.hasPrev && !this.data.loading) {
      const prevPage = this.data.currentPage - 1;
      this.loadWords(prevPage);
      
      // 滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
  },

  // 下一页
  onNextPage: function() {
    if (this.data.hasMore && !this.data.loading) {
      const nextPage = this.data.currentPage + 1;
      this.loadWords(nextPage);
      
      // 滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
  },

  // 跳转到指定页
  onGoToPage: function() {
    const { totalPages } = this.data;
    
    wx.showModal({
      title: '跳转页面',
      content: `请输入页码 (1-${totalPages})`,
      editable: true,
      placeholderText: '输入页码',
      success: (res) => {
        if (res.confirm) {
          const page = parseInt(res.content);
          if (page >= 1 && page <= totalPages) {
            this.loadWords(page);
            // 滚动到顶部
            wx.pageScrollTo({
              scrollTop: 0,
              duration: 300
            });
          } else {
            wx.showToast({
              title: '页码超出范围',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 快速跳转页码
  onQuickPage: function(e) {
    const page = parseInt(e.currentTarget.dataset.page);
    if (page !== this.data.currentPage && !this.data.loading) {
      this.loadWords(page);
      // 滚动到顶部
      wx.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    }
  },

  // 搜索
  onSearchInput: function(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  onSearchConfirm: function() {
    this.loadWords(1);
  },

  // 选择词汇
  onWordSelect: function(e) {
    const { index } = e.currentTarget.dataset;
    const word = this.data.words[index];
    
    console.log('=== 点击选择词汇 ===');
    console.log('词汇:', word);
    console.log('当前选择:', this.data.selectedWords);
    
    // 检查VIP限制
    if (!this.checkWordSelectable(index)) {
      wx.showModal({
        title: '提示',
        content: '免费用户只能选择前100个词汇，开通会员解锁全部词汇',
        confirmText: '去开通',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({ url: '/pages/vip/vip' });
          }
        }
      });
      return;
    }

    const selectedWords = [...this.data.selectedWords];
    const existIndex = selectedWords.findIndex(w => w._id === word._id);
    
    // 更新词汇列表中的selected状态
    const words = [...this.data.words];
    words[index] = { ...words[index], selected: existIndex === -1 };
    
    if (existIndex > -1) {
      selectedWords.splice(existIndex, 1);
      console.log('取消选择词汇:', word.words);
    } else {
      selectedWords.push(word);
      console.log('选择词汇:', word.words);
    }
    
    this.setData({ 
      selectedWords,
      words 
    });
    console.log('更新后选择:', this.data.selectedWords.length, '个');
  },

  // 检查词汇是否可选择
  checkWordSelectable: function(index) {
    const userInfo = getApp().globalData.userInfo;
    const hasVipAccess = VIP_CONFIG.checkVipAccess(userInfo);
    
    return hasVipAccess || index < 100;
  },

  // 是否已选择
  isWordSelected: function(wordId) {
    return this.data.selectedWords.some(w => w._id === wordId);
  },

  // 全选免费词汇
  selectFreeWords: function() {
    const freeWords = this.data.words.slice(0, 100);
    
    // 更新词汇列表的selected状态
    const words = this.data.words.map((word, index) => ({
      ...word,
      selected: index < 100
    }));
    
    this.setData({ 
      selectedWords: freeWords,
      words 
    });
  },

  // 清空选择
  clearSelection: function() {
    // 更新词汇列表的selected状态
    const words = this.data.words.map(word => ({
      ...word,
      selected: false
    }));
    
    this.setData({ 
      selectedWords: [],
      words 
    });
  },

  // 按顺序学习全部
  studyInOrder: function() {
    wx.showLoading({ title: '正在加载词汇，请稍候...' });
    
    this.loadAllWords().then(allWords => {
      wx.hideLoading();
      if (allWords.length === 0) {
        wx.showToast({ title: '暂无词汇', icon: 'error' });
        return;
      }
      
      console.log('=== 按顺序学习全部词汇 ===');
      console.log('词汇总数:', allWords.length);
      console.log('预设测试模式:', this.data.presetTestMode);
      
      // 如果有预设测试模式，直接开始学习，不显示书籍确认界面
      if (this.data.presetTestMode) {
        // 消消乐模式使用界面上已选择的每组词汇数量
        if (this.data.presetTestMode === 'elimination') {
          this.showEliminationGroupingAndStart(allWords, false, this.data.wordsPerGroup);
        } else {
          // 其他模式直接跳转到模式选择页面
          try {
            this.startLearning(allWords, this.data.presetTestMode, false);
          } catch (error) {
            console.error('开始学习出错:', error);
            wx.showToast({ title: '启动失败，请重试', icon: 'error' });
          }
        }
        return;
      }
      
      // 检查是否有学习进度，决定是否显示进度选择或模式选择
      this.checkProgressAndShowOptions(allWords, false);
    }).catch(error => {
      wx.hideLoading();
      console.error('加载全部词汇失败:', error);
      wx.showToast({ title: '加载失败，请重试', icon: 'error' });
    });
  },

  // 乱序学习全部
  studyRandom: function() {
    wx.showLoading({ title: '正在加载词汇，请稍候...' });
    
    this.loadAllWords().then(allWords => {
      wx.hideLoading();
      if (allWords.length === 0) {
        wx.showToast({ title: '暂无词汇', icon: 'error' });
        return;
      }
      
      console.log('=== 乱序学习全部词汇 ===');
      console.log('词汇总数:', allWords.length);
      console.log('预设测试模式:', this.data.presetTestMode);
      
      // 打乱词汇顺序
      const shuffledWords = [...allWords].sort(() => Math.random() - 0.5);
      
      // 如果有预设测试模式，直接开始学习，不显示书籍确认界面
      if (this.data.presetTestMode) {
        // 消消乐模式使用界面上已选择的每组词汇数量
        if (this.data.presetTestMode === 'elimination') {
          this.showEliminationGroupingAndStart(shuffledWords, true, this.data.wordsPerGroup);
        } else {
          // 其他模式直接跳转到模式选择页面
          try {
            this.startLearning(shuffledWords, this.data.presetTestMode, true);
          } catch (error) {
            console.error('开始学习出错:', error);
            wx.showToast({ title: '启动失败，请重试', icon: 'error' });
          }
        }
        return;
      }
      
      // 检查是否有学习进度，决定是否显示进度选择或模式选择
      this.checkProgressAndShowOptions(shuffledWords, true);
    }).catch(error => {
      wx.hideLoading();
      console.error('加载全部词汇失败:', error);
      wx.showToast({ title: '加载失败，请重试', icon: 'error' });
    });
  },

  // 开始全书学习（相当于从首页进入学习）
  startFullBookLearning: function(words, isRandom) {
    console.log('=== 开始全书学习 ===');
    console.log('词汇数量:', words.length);
    console.log('是否乱序:', isRandom);
    
    // 存储学习数据到全局，供学习页面使用
    const app = getApp();
    app.globalData.fullBookLearningData = {
      words: words,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      isRandom: isRandom,
      startTime: Date.now(),
      currentProgress: this.getStoredProgress() || 0 // 获取已保存的进度
    };
    
    console.log('全书学习数据已存储:', app.globalData.fullBookLearningData);
    
    // 跳转到学习进度页面，让用户选择从头开始还是继续学习
    wx.navigateTo({
      url: `/pages/wordtest/progress/progress?libraryId=${this.data.libraryId}&mode=fullbook&isRandom=${isRandom}`,
      success: () => {
        console.log('跳转到学习进度页面成功');
      },
      fail: (err) => {
        console.error('跳转到学习进度页面失败:', err);
        // 如果进度页面不存在，直接跳转到单词测试页面
        wx.navigateTo({
          url: `/pages/wordtest/wordtest?libraryId=${this.data.libraryId}&mode=fullbook&isRandom=${isRandom}`,
          success: () => {
            console.log('跳转到单词测试页面成功');
          },
          fail: (err2) => {
            console.error('跳转到单词测试页面也失败:', err2);
            wx.showToast({ title: '跳转失败', icon: 'error' });
          }
        });
      }
    });
  },

  // 检查进度并显示选项（先选模式，再检查进度）
  checkProgressAndShowOptions: function(words, isRandom) {
    console.log('=== 显示模式选择 ===');
    
    // 直接显示模式选择，在选择模式后再检查该模式的进度
    this.showModeSelector(words, isRandom);
  },

  // 显示进度选择（继续/重新开始）
  showProgressOptions: function(words, isRandom, savedProgress) {
    const progressText = `上次学习进度：${savedProgress.percentage}%（${savedProgress.currentIndex}/${savedProgress.totalCount}）`;
    
    wx.showModal({
      title: '学习进度',
      content: progressText + '\n\n请选择：',
      confirmText: '继续学习',
      cancelText: '重新开始',
      success: (res) => {
        if (res.confirm) {
          // 继续学习 - 显示模式选择，但保持进度
          this.showModeSelector(words, isRandom, savedProgress);
        } else {
          // 重新开始 - 清除进度，显示模式选择
          this.clearProgress();
          this.showModeSelector(words, isRandom);
        }
      }
    });
  },

  // 清除学习进度
  clearProgress: function(mode = 'default') {
    try {
      const progressKey = `progress_${this.data.libraryId}_${mode}`;
      wx.removeStorageSync(progressKey);
      console.log('学习进度已清除:', progressKey);
    } catch (error) {
      console.error('清除学习进度失败:', error);
    }
  },

  // 获取存储的学习进度
  getStoredProgress: function(mode = 'default') {
    try {
      const progressKey = `progress_${this.data.libraryId}_${mode}`;
      const progress = wx.getStorageSync(progressKey);
      console.log('获取到的学习进度:', progressKey, progress);
      return progress || null;
    } catch (error) {
      console.error('获取学习进度失败:', error);
      return null;
    }
  },

  // 检查特定模式的进度并开始学习
  checkModeProgressAndStart: function(words, mode, isRandom) {
    console.log('=== 检查模式进度 ===', mode);
    
    // 获取该模式的学习进度
    const savedProgress = this.getStoredProgress(mode);
    
    if (savedProgress && savedProgress.currentIndex > 0) {
      // 有进度，显示继续/重新开始选择
      const progressText = `${mode}模式进度：${savedProgress.percentage}%（${savedProgress.currentIndex}/${savedProgress.totalCount}）`;
      
      wx.showModal({
        title: '继续学习',
        content: progressText + '\n\n请选择：',
        confirmText: '继续学习',
        cancelText: '重新开始',
        success: (res) => {
          if (res.confirm) {
            // 继续学习，保持进度
            this.startModeWithProgress(words, mode, isRandom, savedProgress);
          } else {
            // 重新开始，清除进度
            this.clearProgress(mode);
            this.startModeWithProgress(words, mode, isRandom, null);
          }
        }
      });
    } else {
      // 没有进度，直接开始
      this.startModeWithProgress(words, mode, isRandom, null);
    }
  },

  // 根据模式开始学习
  startModeWithProgress: function(words, mode, isRandom, savedProgress) {
    // 针对不同模式设置分页逻辑
    if (mode === 'dictation') {
      // 听写模式：直接开始测试
      this.startDictationTestDirectly(words, savedProgress);
    } else if (mode === 'elimination') {
      // 消消乐模式：直接开始游戏
      this.startEliminationGameDirectly(words, savedProgress);
    } else {
      // 英译汉、汉译英：直接开始测试
      this.startWordTestDirectly(words, mode, savedProgress);
    }
  },

  // 直接开始英译汉/汉译英测试
  startWordTestDirectly: function(words, mode, savedProgress) {
    console.log('=== 直接开始词汇测试 ===', mode);

    // 存储学习数据到全局
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: mode,
      isRandom: false,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      savedProgress: savedProgress
    };

    // 如果有分组信息，添加到全局数据
    if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
      const { currentGroup, totalGroups, wordsPerGroup } = savedProgress.groupInfo;
      app.globalData.learningData.isGrouped = true;
      app.globalData.learningData.currentGroup = currentGroup;
      app.globalData.learningData.totalGroups = totalGroups;
      app.globalData.learningData.wordsPerGroup = wordsPerGroup;
      app.globalData.learningData.originalWords = savedProgress.allWords || words;
    }

    // 直接跳转到测试页面
    const url = `/pages/wordtest/test/test?testMode=${mode}&shareMode=self&perQuestionTime=15`;
    console.log('直接跳转到测试页面:', url);

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('直接跳转到测试页面成功');
      },
      fail: (err) => {
        console.error('直接跳转到测试页面失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 直接开始听写测试
  startDictationTestDirectly: function(words, savedProgress) {
    console.log('=== 直接开始听写测试 ===');

    // 存储学习数据到全局
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: 'dictation',
      isRandom: false,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      savedProgress: savedProgress
    };

    // 如果有分组信息，添加到全局数据
    if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
      const { currentGroup, totalGroups, wordsPerGroup } = savedProgress.groupInfo;
      app.globalData.learningData.isGrouped = true;
      app.globalData.learningData.currentGroup = currentGroup;
      app.globalData.learningData.totalGroups = totalGroups;
      app.globalData.learningData.wordsPerGroup = wordsPerGroup;
      app.globalData.learningData.originalWords = savedProgress.allWords || words;
    }

    // 直接跳转到听写测试页面
    const url = `/pages/spelling/practice/practice?testMode=dictation&shareMode=self&perQuestionTime=15`;
    console.log('直接跳转到听写测试页面:', url);

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('直接跳转到听写测试页面成功');
      },
      fail: (err) => {
        console.error('直接跳转到听写测试页面失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 直接开始消消乐游戏
  startEliminationGameDirectly: function(words, savedProgress) {
    console.log('=== 直接开始消消乐游戏 ===');

    // 存储学习数据到全局
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: 'elimination',
      isRandom: false,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      savedProgress: savedProgress
    };

    // 如果有分组信息，添加到全局数据
    if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
      const { currentGroup, totalGroups, wordsPerGroup } = savedProgress.groupInfo;
      app.globalData.learningData.isGrouped = true;
      app.globalData.learningData.currentGroup = currentGroup;
      app.globalData.learningData.totalGroups = totalGroups;
      app.globalData.learningData.wordsPerGroup = wordsPerGroup;
      app.globalData.learningData.originalWords = savedProgress.allWords || words;
    }

    // 直接跳转到消消乐游戏页面，使用默认的10个词汇一组
    const url = `/pages/task/puzzle/puzzle?testMode=elimination&shareMode=self&wordsPerGroup=10`;
    console.log('直接跳转到消消乐游戏页面:', url);

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('直接跳转到消消乐游戏页面成功');
      },
      fail: (err) => {
        console.error('直接跳转到消消乐游戏页面失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 继续学习指定模式（从学习进度页面进入）
  continueStudyWithMode: function(targetMode) {
    console.log('=== 继续学习指定模式 ===', targetMode);

    // 检查该模式的学习进度
    const savedProgress = this.getStoredProgress(targetMode);

    // 如果是分组学习，需要加载全部词汇
    if (savedProgress && savedProgress.isGrouped) {
      console.log('检测到分组学习，需要加载全部词汇');
      this.loadAllWords().then(allWords => {
        this.continueStudyWithLoadedWords(allWords, targetMode);
      }).catch(error => {
        console.error('加载全部词汇失败:', error);
        wx.showToast({ title: '加载失败，请重试', icon: 'error' });
      });
      return;
    }

    // 普通学习模式，使用当前已加载的词汇
    if (!this.data.words || this.data.words.length === 0) {
      console.log('词汇数据未加载，需要先加载词汇');
      this.loadAllWords().then(allWords => {
        this.continueStudyWithLoadedWords(allWords, targetMode);
      }).catch(error => {
        console.error('加载词汇失败:', error);
        wx.showToast({ title: '加载失败，请重试', icon: 'error' });
      });
      return;
    }

    this.continueStudyWithLoadedWords(this.data.words, targetMode);
  },

  // 使用已加载的词汇继续学习
  continueStudyWithLoadedWords: function(words, targetMode) {
    console.log('=== 使用已加载词汇继续学习 ===', targetMode, '词汇数量:', words.length);

    // 检查该模式的学习进度
    const savedProgress = this.getStoredProgress(targetMode);

    if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
      // 分组学习模式
      console.log('检测到分组学习进度:', savedProgress.groupInfo);

      const { currentGroup, totalGroups, wordsPerGroup, completedGroups } = savedProgress.groupInfo;

      // 计算下一组的起始位置
      let nextGroup = currentGroup;
      if (completedGroups && completedGroups.includes(currentGroup)) {
        // 当前组已完成，进入下一组
        nextGroup = currentGroup + 1;
      }

      if (nextGroup > totalGroups) {
        // 所有组都已完成
        wx.showModal({
          title: '学习完成',
          content: `恭喜！您已完成${this.data.libraryName}的${this.getModeText(targetMode)}模式学习。`,
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      // 计算当前组的词汇
      const startIndex = (nextGroup - 1) * wordsPerGroup;
      const endIndex = Math.min(startIndex + wordsPerGroup, words.length);
      const groupWords = words.slice(startIndex, endIndex);

      console.log(`继续第${nextGroup}组学习，词汇范围: ${startIndex}-${endIndex}`);

      // 更新进度信息
      const updatedProgress = {
        ...savedProgress,
        groupInfo: {
          ...savedProgress.groupInfo,
          currentGroup: nextGroup
        }
      };

      // 开始当前组的学习
      this.startModeWithProgress(groupWords, targetMode, false, updatedProgress);

    } else if (savedProgress && savedProgress.currentIndex > 0) {
      // 普通学习模式，有进度
      console.log('继续普通学习模式，进度:', savedProgress.currentIndex, '/', savedProgress.totalCount);
      this.startModeWithProgress(words, targetMode, false, savedProgress);
    } else {
      // 没有进度，从头开始
      console.log('没有学习进度，从头开始');
      this.startModeWithProgress(words, targetMode, false, null);
    }
  },

  // 获取模式显示文本
  getModeText: function(mode) {
    const modeTexts = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐'
    };
    return modeTexts[mode] || mode;
  },

  // 开始分页学习（听写模式等）
  startPagedLearning: function(words, mode, isRandom, wordsPerPage, savedProgress = null) {
    console.log('=== 开始分页学习 ===');
    console.log('模式:', mode, '每页词汇数:', wordsPerPage, '总词汇数:', words.length);

    const totalGroups = Math.ceil(words.length / wordsPerPage);
    let startGroup = 1;

    // 如果有保存的进度，计算起始组
    if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
      startGroup = savedProgress.groupInfo.currentGroup;
      console.log('从第', startGroup, '组继续学习');
    } else if (savedProgress && savedProgress.currentIndex > 0) {
      startGroup = Math.floor(savedProgress.currentIndex / wordsPerPage) + 1;
      console.log('从第', startGroup, '组继续学习（根据索引计算）');
    }

    // 存储分页学习数据
    const app = getApp();
    app.globalData.pagedLearningData = {
      words: words,
      mode: mode,
      isRandom: isRandom,
      wordsPerGroup: wordsPerPage, // 改名为wordsPerGroup更准确
      totalGroups: totalGroups,    // 改名为totalGroups更准确
      currentGroup: startGroup,    // 改名为currentGroup更准确
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName,
      startTime: Date.now(),
      // 保持向后兼容
      wordsPerPage: wordsPerPage,
      totalPages: totalGroups,
      currentPage: startGroup
    };

    // 跳转到对应的学习页面
    this.startLearningWithPagination(words, mode, isRandom, startGroup, wordsPerPage);
  },

  // 显示消消乐分组选择
  showEliminationGrouping: function(words, isRandom, savedProgress = null) {
    const groupOptions = [10, 15, 20, 25, 30];
    const maxWords = Math.min(words.length, 100); // 消消乐最多100个词
    const availableOptions = groupOptions.filter(option => option <= maxWords);
    
    wx.showActionSheet({
      itemList: availableOptions.map(option => {
        const groups = Math.ceil(maxWords / option);
        return `每组${option}个词汇（共${groups}组）`;
      }),
      success: (res) => {
        const selectedWordsPerGroup = availableOptions[res.tapIndex];
        this.startPagedLearning(words.slice(0, maxWords), 'elimination', isRandom, selectedWordsPerGroup, savedProgress);
      }
    });
  },

  // 开始带分页的学习
  startLearningWithPagination: function(words, mode, isRandom, startGroup, wordsPerGroup) {
    // 计算当前组的词汇
    const startIndex = (startGroup - 1) * wordsPerGroup;
    const endIndex = Math.min(startIndex + wordsPerGroup, words.length);
    const currentGroupWords = words.slice(startIndex, endIndex);

    console.log('当前组词汇:', currentGroupWords.length, '个，从', startIndex, '到', endIndex);
    
    // 根据模式跳转到相应页面
    const totalGroups = Math.ceil(words.length / wordsPerGroup);

    switch (mode) {
      case 'dictation':
        wx.navigateTo({
          url: `/pages/spelling/practice/practice?mode=dictation&words=${encodeURIComponent(JSON.stringify(currentGroupWords))}&currentGroup=${startGroup}&totalGroups=${totalGroups}&wordsPerGroup=${wordsPerGroup}&libraryId=${this.data.libraryId}&libraryName=${encodeURIComponent(this.data.libraryName)}`
        });
        break;
      case 'elimination':
        wx.navigateTo({
          url: `/pages/task/puzzle/puzzle?words=${encodeURIComponent(JSON.stringify(currentGroupWords))}&currentGroup=${startGroup}&totalGroups=${totalGroups}&wordsPerGroup=${wordsPerGroup}&libraryId=${this.data.libraryId}&libraryName=${encodeURIComponent(this.data.libraryName)}`
        });
        break;
      default:
        // 英译汉、汉译英等
        this.startLearning(words, mode, isRandom);
    }
  },

  // 保存学习进度
  saveProgress: function(currentIndex, totalCount, mode = 'default', groupInfo = null) {
    try {
      const progressKey = `progress_${this.data.libraryId}_${mode}`;
      const progressData = {
        currentIndex: currentIndex,
        totalCount: totalCount,
        percentage: Math.round((currentIndex / totalCount) * 100),
        lastStudyTime: Date.now(),
        mode: mode,
        libraryId: this.data.libraryId,
        libraryName: this.data.libraryName
      };

      // 如果是分组学习，添加分组信息
      if (groupInfo) {
        progressData.isGrouped = true;
        progressData.groupInfo = {
          currentGroup: groupInfo.currentGroup,
          totalGroups: groupInfo.totalGroups,
          wordsPerGroup: groupInfo.wordsPerGroup,
          completedGroups: groupInfo.completedGroups || [],
          groupProgress: Math.round((groupInfo.currentGroup / groupInfo.totalGroups) * 100)
        };

        // 更新进度显示文本，优先显示分组进度
        progressData.progressText = `第${groupInfo.currentGroup}/${groupInfo.totalGroups}组`;
        progressData.detailText = `已完成${groupInfo.completedGroups.length}组，共${groupInfo.totalGroups}组`;
      }

      wx.setStorageSync(progressKey, progressData);
      console.log('学习进度已保存:', progressKey, progressData);
    } catch (error) {
      console.error('保存学习进度失败:', error);
    }
  },

  // 保存分组学习进度
  saveGroupProgress: function(mode, currentGroup, totalGroups, wordsPerGroup, completedGroups = []) {
    try {
      const progressKey = `progress_${this.data.libraryId}_${mode}`;

      // 获取现有进度数据
      let progressData = wx.getStorageSync(progressKey) || {};

      // 更新分组信息
      progressData = {
        ...progressData,
        libraryId: this.data.libraryId,
        libraryName: this.data.libraryName,
        mode: mode,
        lastStudyTime: Date.now(),
        isGrouped: true,
        groupInfo: {
          currentGroup: currentGroup,
          totalGroups: totalGroups,
          wordsPerGroup: wordsPerGroup,
          completedGroups: completedGroups,
          groupProgress: Math.round((completedGroups.length / totalGroups) * 100)
        },
        // 计算总体进度
        currentIndex: completedGroups.length * wordsPerGroup,
        totalCount: totalGroups * wordsPerGroup,
        percentage: Math.round((completedGroups.length / totalGroups) * 100),
        progressText: `第${currentGroup}/${totalGroups}组`,
        detailText: `已完成${completedGroups.length}组，共${totalGroups}组`
      };

      wx.setStorageSync(progressKey, progressData);
      console.log('分组学习进度已保存:', progressKey, progressData);

      return progressData;
    } catch (error) {
      console.error('保存分组学习进度失败:', error);
      return null;
    }
  },

  // 自定义选择学习
  studyCustom: function() {
    if (this.data.selectedWords.length === 0) {
      wx.showToast({ title: '请选择词汇', icon: 'error' });
      return;
    }

    // 检查是否有预设的测试模式，如果有直接跳转到对应的模式选择页面
    if (this.data.presetTestMode) {
      this.startLearning(this.data.selectedWords, this.data.presetTestMode, false);
      return;
    }

    // 如果没有预设模式，显示模式选择弹窗，让用户选择学习模式
    this.showModeSelector(this.data.selectedWords, false);
  },

  // 显示学习模式选择器
  showModeSelector: function(words, isRandom, savedProgress = null) {
    // 如果有预设的测试模式，直接开始学习
    if (this.data.presetTestMode) {
      this.startLearning(words, this.data.presetTestMode, isRandom, savedProgress);
      return;
    }

    const modes = [
      { key: 'en_to_cn', name: '英译汉', desc: '看英文选中文' },
      { key: 'cn_to_en', name: '汉译英', desc: '看中文写英文' },
      { key: 'dictation', name: '单词听写', desc: '听音频写单词' },
      { key: 'elimination', name: '单词消消乐', desc: '趣味消除游戏' }
    ];

    wx.showActionSheet({
      itemList: modes.map(mode => `${mode.name} - ${mode.desc}`),
      success: (res) => {
        const selectedMode = modes[res.tapIndex];
        
        // 直接跳转到对应模式的"练习模式或测试模式"选择页面
        this.startLearning(words, selectedMode.key, isRandom, savedProgress);
      }
    });
  },

  // 检查进度并开始学习
  checkProgressAndStart: function(words, mode, isRandom) {
    // 如果是消消乐模式，直接开始（消消乐不需要进度记录）
    if (mode === 'elimination') {
      this.startLearning(words, mode, isRandom);
      return;
    }

    // 检查是否应该显示进度页面
    const shouldShowProgress = this.shouldShowProgressPage(words, isRandom);
    
    if (shouldShowProgress) {
      // 跳转到进度页面
      wx.navigateTo({
        url: `/pages/wordtest/progress/progress?libraryId=${this.data.libraryId}&testMode=${mode}`
      });
    } else {
      // 直接开始学习
      this.startLearning(words, mode, isRandom);
    }
  },

  // 判断是否应该显示进度页面（多关卡模式）
  shouldShowProgressPage: function(words, isRandom) {
    const wordsPerGroup = this.data.wordsPerGroup || 10; // 获取每组词汇数量设置
    
    // 如果选择的词汇数量大于每组词汇数量，就是多关卡模式
    if (words.length > wordsPerGroup) {
      console.log(`选择了${words.length}个词汇，超过每组${wordsPerGroup}个的设置，启用多关卡模式`);
      return true;
    }
    
    // 否则为单一关卡模式
    console.log(`选择了${words.length}个词汇，未超过每组${wordsPerGroup}个的设置，使用单一关卡模式`);
    return false;
  },

  // 获取总词汇数量（用于判断是否为全部学习）
  getTotalWordsCount: function() {
    // 这里可以通过云函数获取，暂时用当前加载的数量估算
    return this.data.words.length;
  },

  // 开始学习（自定义选择词汇时，跳转到模式选择页面）
  startLearning: function(words, mode, isRandom, savedProgress = null) {
    console.log('=== 开始学习 ===');
    console.log('words数量:', words.length);
    console.log('第一个词汇:', words[0]);
    console.log('学习模式:', mode);
    console.log('是否乱序:', isRandom);
    console.log('分享模式:', this.data.shareMode);

    if (!words || words.length === 0) {
      wx.showToast({ title: '没有可学习的词汇', icon: 'error' });
      return;
    }

    // 分享创建模式 - 直接创建分享测试
    if (this.data.shareMode === 'create') {
      this.createShareTest(words, mode, isRandom, this.data.isPhrase);
      return;
    }

    // 创建竞赛模式 - 直接创建竞赛
    if (this.data.createCompetition) {
      this.createCompetition(words, mode, isRandom, this.data.isPhrase);
      return;
    }

    // 听写模式跳转到专门的模式选择页面
    if (mode === 'dictation') {
      // 存储学习数据到全局
      const app = getApp();
      app.globalData.learningData = {
        words: words,
        mode: mode,
        isRandom: isRandom,
        libraryId: this.data.libraryId,
        libraryName: this.data.libraryName,
        savedProgress: savedProgress // 包含进度信息
      };

      // 跳转到听写模式选择页面（使用类似英译汉的全屏页面）
      const url = `/pages/spelling/mode-select/mode-select?testMode=dictation&total=${words.length}&libraryId=${this.data.libraryId}`;
      console.log('跳转到听写模式选择页面:', url);
      
      wx.navigateTo({
        url: url,
        success: () => {
          console.log('跳转到听写模式选择页面成功');
        },
        fail: (err) => {
          console.error('跳转到听写模式选择页面失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
      return;
    }

    // 所有测试模式（包括消消乐）都跳转到模式选择页面
    if (mode === 'en_to_cn' || mode === 'cn_to_en' || mode === 'elimination') {
      // 存储学习数据到全局
      const app = getApp();
      app.globalData.learningData = {
        words: words,
        mode: mode,
        isRandom: isRandom,
        libraryId: this.data.libraryId,
        libraryName: this.data.libraryName,
        savedProgress: savedProgress // 包含进度信息
      };

      // 构建URL参数，包含分组信息
      let url = `/pages/wordtest/mode-select/mode-select?testMode=${mode}&total=${words.length}&libraryId=${this.data.libraryId}`;

      // 如果有分组信息，添加到URL参数中
      if (savedProgress && savedProgress.isGrouped && savedProgress.groupInfo) {
        const { currentGroup, totalGroups, wordsPerGroup } = savedProgress.groupInfo;
        url += `&isGrouped=true&currentGroup=${currentGroup}&totalGroups=${totalGroups}&wordsPerGroup=${wordsPerGroup}`;
        console.log('添加分组信息到URL:', { currentGroup, totalGroups, wordsPerGroup });
      }

      console.log('跳转到模式选择页面:', url);
      
      wx.navigateTo({
        url: url,
        success: () => {
          console.log('页面跳转成功');
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
      return;
    }

    // 其他模式直接跳转到学习页面
    this.startLearningDirectly(words, mode, isRandom);
  },

  /**
   * 创建竞赛
   */
  createCompetition: function(words, mode, isRandom, isPhrase = false) {
    console.log('=== 创建竞赛 ===');
    console.log('竞赛模式:', mode);
    console.log('单词数量:', words.length);
    console.log('是否乱序:', isRandom);
    console.log('是否短语模式:', isPhrase);

    if (words.length === 0) {
      wx.showToast({
        title: '请选择单词',
        icon: 'none'
      });
      return;
    }

    // 检查用户登录状态
    const app = getApp();
    if (!app.isLoggedIn()) {
      wx.showModal({
        title: '需要登录',
        content: '创建竞赛需要登录，是否立即登录？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/login/login'
            });
          }
        }
      });
      return;
    }

    // 输入竞赛名称
    let competitionName = '';
    const modalInput = wx.showModal({
      title: '创建竞赛',
      content: '',
      editable: true,
      placeholderText: '',
      success: async (res) => {
        if (res.confirm && res.content.trim()) {
          competitionName = res.content.trim();
          
          wx.showLoading({
            title: '创建中...',
            mask: true
          });

          try {
            // 根据不同模式转换testMode
            let competitionMode = mode;
            if (mode === 'en_to_cn') {
              competitionMode = 'en2zh';
            } else if (mode === 'cn_to_en') {
              competitionMode = 'zh2en';
            } else if (mode === 'elimination') {
              competitionMode = 'elimination';
            } else if (mode === 'dictation') {
              competitionMode = 'dictation';
            }

            // 判断是否需要创建多关卡竞赛
            const wordsPerGroup = this.data.wordsPerGroup || 10;
            const needMultiLevel = words.length > wordsPerGroup;
            
            console.log('创建竞赛判断:', {
              wordCount: words.length,
              wordsPerGroup: wordsPerGroup,
              needMultiLevel: needMultiLevel
            });

            // 超级优化：判断是否使用基于索引的存储策略
            const isSystemLibrary = this.data.libraryId &&
                                    !this.data.libraryId.includes('custom') &&
                                    !this.data.libraryId.includes('mistake');
            const isLargeLibrary = words && words.length >= 100;
            const useIndexBasedStorage = isSystemLibrary && isLargeLibrary;

            console.log('竞赛存储策略分析:', {
              libraryId: this.data.libraryId,
              wordsCount: words?.length,
              isSystemLibrary,
              isLargeLibrary,
              useIndexBasedStorage,
              isRandomOrder: isRandom
            });

            // 如果是短语模式，需要调整竞赛模式名称
            const actualCompetitionMode = isPhrase ? `phrase_${competitionMode}` : competitionMode;

            // 调用云函数创建竞赛
            const result = await wx.cloud.callFunction({
              name: 'createCompetition',
              data: {
                name: competitionName,
                mode: actualCompetitionMode,
                // 超级优化：基于索引存储时只传递必要信息
                words: useIndexBasedStorage ?
                  [{
                    _id: words[0]._id,
                    words: words[0].words,
                    totalCount: words.length
                  }] :
                  words,
                libraryId: this.data.libraryId,
                libraryName: this.data.libraryName,
                // 传递乱序信息，支持基于索引的存储
                isRandomOrder: isRandom || false,
                isPhrase: isPhrase || false, // 添加短语标识
                wordsPerGroup: needMultiLevel ? wordsPerGroup : undefined // 只有多关卡时才传递分组参数
              }
            });

            wx.hideLoading();

            if (result.result.success) {
              wx.showModal({
                title: '创建成功',
                content: `竞赛"${competitionName}"创建成功！现在可以分享给好友参与了。`,
                showCancel: true,
                cancelText: '稍后分享',
                confirmText: '立即查看',
                success: (shareRes) => {
                  if (shareRes.confirm) {
                    // 跳转到竞赛页面，切换到对应模式
                    wx.navigateTo({
                      url: `/pages/competition/competition?mode=${competitionMode}&highlightId=${result.result.competitionId}`
                    });
                  } else {
                    // 跳转到竞赛列表
                    wx.navigateTo({
                      url: `/pages/competition/competition?mode=${competitionMode}`
                    });
                  }
                }
              });
            } else {
              throw new Error(result.result.message || '创建失败');
            }
          } catch (error) {
            wx.hideLoading();
            console.error('创建竞赛失败:', error);
            wx.showToast({
              title: '创建失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 创建分享测试
   */
  createShareTest: function(words, testMode, isRandom, isPhrase = false) {
    console.log('=== 创建分享测试 ===');
    console.log('测试模式:', testMode);
    console.log('单词数量:', words.length);
    console.log('是否乱序:', isRandom);
    console.log('是否短语模式:', isPhrase);

    if (words.length === 0) {
      wx.showToast({
        title: '请选择单词',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '创建分享测试...',
      mask: true
    });

    try {
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      // 如果是短语模式，在测试模式前加上phrase_前缀
      const actualTestMode = isPhrase ? `phrase_${testMode}` : testMode;
      const shareId = actualTestMode + '_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      // 处理消消乐模式的词汇分组
      let shareTestData;
      if (testMode === 'elimination') {
        const maxWordsPerGroup = this.data.wordsPerGroup || this.calculateMaxWords();
        const totalGroups = Math.ceil(words.length / maxWordsPerGroup);
        const firstGroupWords = words.slice(0, maxWordsPerGroup);
        
        shareTestData = {
          shareId: shareId,
          testMode: actualTestMode,
          words: firstGroupWords, // 第一关的词汇
          allWords: words, // 保存所有词汇，用于多关卡
          wordsPerGroup: maxWordsPerGroup, // 保存每组词汇数量
          totalGroups: totalGroups,
          currentGroup: 1,
          isRandom: isRandom,
          isPhrase: isPhrase, // 添加短语标识
          eliminationGameMode: 'practice', // 默认练习模式
          createdBy: currentUser.nickName || '匿名用户',
          creatorInfo: {
            openid: currentUser.openid,
            nickName: currentUser.nickName,
            avatarUrl: currentUser.avatarUrl
          },
          createTime: Date.now(),
          visitors: [],
          results: []
        };
      } else {
        // 其他测试模式
        shareTestData = {
          shareId: shareId,
          testMode: actualTestMode,
          words: words,
          isRandom: isRandom,
          isPhrase: isPhrase, // 添加短语标识
          libraryId: this.data.libraryId,
          libraryName: this.data.libraryName,
          createdBy: currentUser.nickName || '匿名用户',
          creatorInfo: {
            openid: currentUser.openid,
            nickName: currentUser.nickName,
            avatarUrl: currentUser.avatarUrl
          },
          createTime: Date.now(),
          visitors: [],
          results: [],
          // 添加分享者的当前学习进度信息
          creatorProgress: this.getStoredProgress(testMode) || {
            currentIndex: 0,
            totalCount: words.length,
            percentage: 0,
            lastStudyTime: Date.now(),
            mode: testMode,
            libraryId: this.data.libraryId,
            libraryName: this.data.libraryName
          },
          // 添加是否允许查看进度的标志
          allowProgressView: true
        };
      }

      // 保存到本地存储
      const shareTests = wx.getStorageSync('shareTests') || {};
      shareTests[shareId] = shareTestData;
      wx.setStorageSync('shareTests', shareTests);

      wx.hideLoading();
      
      // 显示分享选项
      wx.showActionSheet({
        itemList: ['复制分享链接', '分享到微信', '查看分享管理'],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.copyShareLink(shareId, testMode);
          } else if (res.tapIndex === 1) {
            this.shareToWeChat(shareId, testMode, words.length);
          } else if (res.tapIndex === 2) {
            this.goToShareManagement();
          }
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('创建分享测试失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'error'
      });
    }
  },

  /**
   * 复制分享链接
   */
  copyShareLink: function(shareId, testMode) {
    let shareUrl = '';
    if (testMode === 'elimination') {
      shareUrl = `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom`;
    } else if (testMode === 'dictation') {
      shareUrl = `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
    } else {
      shareUrl = `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
    }
    
    wx.setClipboardData({
      data: shareUrl,
      success: () => {
        wx.showToast({ title: '链接已复制', icon: 'success' });
      },
      fail: () => {
        wx.showToast({ title: '复制失败', icon: 'error' });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat: function(shareId, testMode, wordCount) {
    // 获取测试模式的emoji图标
    const modeEmojis = {
      'en_to_cn': '🇨🇳',
      'cn_to_en': '🇺🇸',
      'dictation': '🎧',
      'elimination': '🎮'
    };
    
    const modeNames = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'dictation': '听写',
      'elimination': '消消乐'
    };
    
    const emoji = modeEmojis[testMode] || '📝';
    const modeName = modeNames[testMode] || '测试';
    const shareTitle = `${emoji} ${modeName}测试 - ${wordCount}个单词`;
    
    let sharePath = '';
    if (testMode === 'elimination') {
      sharePath = `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom`;
    } else if (testMode === 'dictation') {
      sharePath = `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
    } else if (testMode.startsWith('phrase_')) {
      // 短语测试需要传递isPhrase参数
      const actualTestMode = testMode === 'phrase_en_to_cn' ? 'en_to_cn' : 'cn_to_en';
      sharePath = `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${actualTestMode}&isPhrase=true`;
    } else {
      sharePath = `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${testMode}`;
    }
    
    // 主动触发分享
    wx.shareAppMessage({
      title: shareTitle,
      path: sharePath,
      imageUrl: '/assets/icons/logo.png',
      success: () => {
        wx.showToast({ title: '分享成功', icon: 'success' });
      },
      fail: () => {
        wx.showToast({ title: '分享取消', icon: 'none' });
      }
    });
  },

  /**
   * 跳转到分享管理
   */
  goToShareManagement: function() {
    wx.navigateTo({
      url: '/pages/profile/share/share',
      fail: (err) => {
        console.error('跳转到我的分享页面失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 直接开始学习（全部学习时，跳过模式选择）
  startLearningDirectly: function(words, mode, isRandom) {
    console.log('=== 直接开始学习 ===');
    console.log('words数量:', words.length);
    console.log('学习模式:', mode);
    console.log('是否乱序:', isRandom);

    if (!words || words.length === 0) {
      wx.showToast({ title: '没有可学习的词汇', icon: 'error' });
      return;
    }

    // 存储学习数据到全局
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: mode,
      isRandom: isRandom,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName
    };

    console.log('存储到全局的数据:', app.globalData.learningData);

    // 英译汉、汉译英、短语测试和消消乐模式都跳转到模式选择页面
    if (mode === 'en_to_cn' || mode === 'cn_to_en' || mode === 'elimination' || mode === 'phrase_en2zh' || mode === 'phrase_zh2en') {
      let url = `/pages/wordtest/mode-select/mode-select?testMode=${mode}&total=${words.length}&libraryId=${this.data.libraryId}`;

      // 消消乐模式需要传递用户选择的每组词汇数量
      if (mode === 'elimination') {
        url += `&wordsPerGroup=${this.data.wordsPerGroup}`;
      }

      console.log('跳转到模式选择页面:', url);

      wx.navigateTo({
        url: url,
        success: () => {
          console.log('页面跳转成功');
        },
        fail: (err) => {
          console.error('页面跳转失败:', err);
          wx.showToast({ title: '跳转失败', icon: 'error' });
        }
      });
      return;
    }

    // 其他模式直接跳转到学习页面
    const url = `/pages/learning/learning?mode=${mode}&total=${words.length}`;
    console.log('跳转URL:', url);
    
    wx.navigateTo({
      url: url,
      success: () => {
        console.log('页面跳转成功');
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 开始消消乐游戏
  startEliminationGame: function(words, isRandom) {
    console.log('=== 开始消消乐游戏 ===');
    
    // 使用用户选择的每组词汇数量
    const MAX_WORDS_PER_GROUP = this.data.wordsPerGroup;
    
    if (words.length <= MAX_WORDS_PER_GROUP) {
      // 词汇数量少于等于设定值，直接开始游戏
      this.startSingleEliminationGame(words, 1, 1);
    } else {
      // 词汇数量大于设定值，需要分组
      const totalGroups = Math.ceil(words.length / MAX_WORDS_PER_GROUP);
      
      // 显示分组提示
      wx.showModal({
        title: '消消乐分组提示',
        content: `您选择了${words.length}个词汇，将分为${totalGroups}个关卡进行，每关最多${MAX_WORDS_PER_GROUP}个词汇。是否继续？`,
        confirmText: '开始游戏',
        cancelText: '重新选择',
        success: (res) => {
          if (res.confirm) {
            // 开始第一关
            const firstGroupWords = words.slice(0, MAX_WORDS_PER_GROUP);
            this.startSingleEliminationGame(firstGroupWords, 1, totalGroups, words);
          }
        }
      });
    }
  },

  // 开始单个消消乐游戏
  startSingleEliminationGame: function(groupWords, currentGroup, totalGroups, allWords = null) {
    // 转换词汇格式为消消乐需要的格式
    const gameWords = groupWords.map(word => ({
      english: word.words || word.word,
      chinese: word.meaning || (word.meanings && word.meanings[0] && word.meanings[0].definitions && word.meanings[0].definitions[0].definition) || '无释义'
    }));

    // 存储游戏数据到全局
    const app = getApp();
    app.globalData.eliminationGameData = {
      words: gameWords,
      currentGroup: currentGroup,
      totalGroups: totalGroups,
      allWords: allWords, // 保存所有词汇，用于下一关
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName
    };

    console.log('消消乐游戏数据:', app.globalData.eliminationGameData);

    // 跳转到消消乐游戏页面
    wx.navigateTo({
      url: `/pages/task/puzzle/puzzle?mode=custom&group=${currentGroup}&total=${totalGroups}`,
      success: () => {
        console.log('跳转到消消乐游戏成功');
      },
      fail: (err) => {
        console.error('跳转到消消乐游戏失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 进入下一步（兼容原逻辑）
  goNext: function() {
    this.studyCustom();
  },

  // 计算最大可容纳的词汇数量（与puzzle页面保持一致）
  calculateMaxWords: function() {
    const containerWidth = 710;
    const containerHeight = 1200;
    const minBlockWidth = 80;
    const minBlockHeight = 40;
    const paddingX = 20;
    const paddingY = 10;
    const spacingX = 15;
    const spacingY = 10;
    
    // 计算最大可能的行列数
    const maxCols = Math.floor((containerWidth - paddingX * 2 + spacingX) / (minBlockWidth + spacingX));
    const maxRows = Math.floor((containerHeight - paddingY * 2 + spacingY) / (minBlockHeight + spacingY));
    
    // 最大位置数 = 最大行数 * 最大列数
    const maxPositions = maxCols * maxRows;
    
    // 最大词汇数 = 最大位置数 / 2
    const maxWords = Math.floor(maxPositions / 2);
    
    console.log('计算最大词汇数:', {
      containerWidth,
      containerHeight,
      maxCols,
      maxRows,
      maxPositions,
      maxWords
    });
    
    return maxWords;
  },

  // 生成每组词汇数量选项
  generateWordsPerGroupOptions: function(maxWords) {
    // 固定的5个选项，10作为默认选项（第一个）
    return [10, 15, 20, 25, 30];
  },

  // 选择每组词汇数量
  onWordsPerGroupSelect: function(e) {
    const value = parseInt(e.currentTarget.dataset.value);
    this.setData({ wordsPerGroup: value });
    
    // 触觉反馈
    wx.vibrateShort();
    
    console.log('选择每组词汇数量:', value);
  },

  // 显示消消乐每组词汇数量选择（用于全部学习）
  showEliminationWordsPerGroupSelection: function(allWords, isRandom) {
    const options = this.generateWordsPerGroupOptions();
    const itemList = options.map(num => `每组 ${num} 个词汇`);
    
    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedWordsPerGroup = options[res.tapIndex];
        this.setData({ wordsPerGroup: selectedWordsPerGroup });
        
        console.log('全部学习选择每组词汇数量:', selectedWordsPerGroup);
        
        // 显示分组提示并开始游戏
        this.showEliminationGroupingAndStart(allWords, isRandom, selectedWordsPerGroup);
      },
      fail: () => {
        console.log('用户取消选择每组词汇数量');
      }
    });
  },

  // 显示消消乐分组提示并跳转到模式选择
  showEliminationGroupingAndStart: function(allWords, isRandom, wordsPerGroup) {
    // 先设置wordsPerGroup到this.data中，确保后续函数能获取到
    this.setData({ wordsPerGroup: wordsPerGroup });
    
    const totalGroups = Math.ceil(allWords.length / wordsPerGroup);
    
    console.log('分组信息:', {
      totalWords: allWords.length,
      wordsPerGroup: wordsPerGroup,
      totalGroups: totalGroups
    });
    
    if (totalGroups === 1) {
      // 只有一组，直接跳转到模式选择页面
      this.startLearningWithWordsPerGroup(allWords, 'elimination', isRandom, wordsPerGroup);
    } else {
      // 多组，显示分组提示
      wx.showModal({
        title: '消消乐分组提示',
        content: `您选择了${allWords.length}个词汇，将分为${totalGroups}个关卡进行，每关最多${wordsPerGroup}个词汇。是否继续？`,
        confirmText: '开始学习',
        cancelText: '重新选择',
        success: (res) => {
          if (res.confirm) {
            // 跳转到模式选择页面
            this.startLearningWithWordsPerGroup(allWords, 'elimination', isRandom, wordsPerGroup);
          } else {
            // 重新选择每组词汇数量
            this.showEliminationWordsPerGroupSelection(allWords, isRandom);
          }
        }
      });
    }
  },

  // 开始学习并传递wordsPerGroup参数
  startLearningWithWordsPerGroup: function(words, mode, isRandom, wordsPerGroup) {
    console.log('=== 开始学习（带wordsPerGroup参数） ===');
    console.log('words数量:', words.length);
    console.log('学习模式:', mode);
    console.log('是否乱序:', isRandom);
    console.log('每组词汇数量:', wordsPerGroup);

    if (!words || words.length === 0) {
      wx.showToast({ title: '没有可学习的词汇', icon: 'error' });
      return;
    }

    // 存储学习数据到全局
    const app = getApp();
    app.globalData.learningData = {
      words: words,
      mode: mode,
      isRandom: isRandom,
      libraryId: this.data.libraryId,
      libraryName: this.data.libraryName
    };

    console.log('存储到全局的数据:', app.globalData.learningData);

    // 构建URL，确保传递wordsPerGroup参数
    let url = `/pages/wordtest/mode-select/mode-select?testMode=${mode}&total=${words.length}&libraryId=${this.data.libraryId}`;

    if (mode === 'elimination') {
      url += `&wordsPerGroup=${wordsPerGroup}`;
    }
    
    console.log('跳转到模式选择页面:', url);
    
    wx.navigateTo({
      url: url,
      success: () => {
        console.log('页面跳转成功');
      },
      fail: (err) => {
        console.error('页面跳转失败:', err);
        wx.showToast({ title: '跳转失败', icon: 'error' });
      }
    });
  },

  // 加载全部词汇
  loadAllWords: function() {
    return new Promise((resolve, reject) => {
      const { libraryId, searchKeyword } = this.data;
      
      console.log('=== 开始加载全部词汇 ===');
      console.log('词库ID:', libraryId);
      console.log('搜索关键词:', searchKeyword);
      
      // 先获取总数
      wx.cloud.callFunction({
        name: 'getWords',
        data: {
          libraryId,
          skip: 0,
          limit: 1,
          keyword: searchKeyword || '',
          getTotalCount: true
        }
      }).then(result => {
        console.log('获取总数结果:', result);
        
        if (result.result.code === 200) {
          const totalCount = result.result.totalCount;
          console.log('数据库总词汇数:', totalCount);
          
          if (!totalCount || totalCount === 0) {
            console.log('词库中没有词汇');
            resolve([]);
            return;
          }
          
          // 分批加载，每批1000个（云数据库限制）
          this.loadAllWordsBatch(libraryId, totalCount, searchKeyword).then(allWords => {
            console.log('分批加载完成，总共获取:', allWords.length, '个词汇');
            resolve(allWords);
          }).catch(error => {
            reject(error);
          });
        } else {
          console.error('获取词汇总数失败:', result.result);
          reject(new Error(result.result.message || '获取词汇总数失败'));
        }
      }).catch(error => {
        console.error('调用云函数失败:', error);
        reject(error);
      });
    });
  },

  // 分批加载所有词汇
  loadAllWordsBatch: function(libraryId, totalCount, searchKeyword) {
    return new Promise((resolve, reject) => {
      const batchSize = 1000; // 云数据库单次查询限制
      const totalBatches = Math.ceil(totalCount / batchSize);
      const allWords = [];
      
      console.log(`开始分批加载，总数${totalCount}，分${totalBatches}批`);
      
      // 创建所有批次的Promise
      const batchPromises = [];
      
      for (let i = 0; i < totalBatches; i++) {
        const skip = i * batchSize;
        const limit = Math.min(batchSize, totalCount - skip);
        
        const promise = wx.cloud.callFunction({
          name: 'getWords',
          data: {
            libraryId,
            skip: skip,
            limit: limit,
            keyword: searchKeyword || ''
          }
        }).then(result => {
          if (result.result.code === 200) {
            console.log(`第${i + 1}批加载完成，获取${result.result.data.length}个词汇`);
            return { index: i, words: result.result.data };
          } else {
            throw new Error(`第${i + 1}批加载失败: ${result.result.message}`);
          }
        });
        
        batchPromises.push(promise);
      }
      
      // 显示加载进度
      wx.showLoading({ title: `正在加载${totalCount}个词汇...` });
      
      // 并发执行所有批次
      Promise.all(batchPromises).then(results => {
        // 按索引排序
        results.sort((a, b) => a.index - b.index);
        
        // 合并所有词汇
        results.forEach(result => {
          allWords.push(...result.words);
        });
        
        console.log(`所有批次完成，总共加载${allWords.length}个词汇`);
        resolve(allWords);
      }).catch(error => {
        console.error('分批加载失败:', error);
        reject(error);
      });
    });
  }
}); 