// pages/profile/share/share.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    shareTestsByMode: {}, // 按模式分组的分享测试 (兼容性保留)
    modeGroups: [], // 新的模式分组数据
    loading: true,
    isEmpty: false,
    expandedModes: {}, // 记录哪些模式是展开的 (兼容性保留)
    isEditMode: false, // 是否处于编辑模式
    selectedTests: [], // 选中的测试ID列表
    showShareModal: false, // 分享弹窗显示状态
    // 分页相关
    allShareTests: [], // 所有分享测试数据
    currentPage: 1, // 当前页码
    pageSize: 10, // 每页显示数量
    totalPages: 1, // 总页数
    totalCount: 0, // 总数量
    showPagination: false, // 是否显示分页
    jumpToPage: '', // 跳转页码输入
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('=== 我的分享页面加载 ===');
    console.log('接收到的参数:', options);
    console.log('页面路径:', getCurrentPages()[getCurrentPages().length - 1].route);

    // 确保初始状态正确
    this.setData({
      isEditMode: false,
      selectedTests: []
    });
    this.loadShareTests();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示时重新加载数据，确保数据是最新的
    this.setData({
      isEditMode: false,
      selectedTests: []
    });
    this.loadShareTests();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadShareTests();
    wx.stopPullDownRefresh();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 加载分享测试列表
   * @param {boolean} forceRefresh - 是否强制刷新，跳过本地缓存
   */
  async loadShareTests(forceRefresh = false) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    try {
      // 获取当前用户信息
      const currentUser = wx.getStorageSync('userInfo') || {};
      const currentUserOpenid = currentUser.openid;

      let myShareTests = [];

      // 优先从云端获取我创建的分享测试数据
      try {
        const cloudResult = await wx.cloud.callFunction({
          name: 'getMyShares',
          data: { type: 'created' }
        });

        if (cloudResult.result.success && cloudResult.result.data) {
          // 处理云端数据
          myShareTests = cloudResult.result.data.map(testData => ({
            shareId: testData.shareId,
            testMode: testData.testType || 'unknown',
            createTime: testData.createTime,
            wordCount: testData.words ? testData.words.length : (testData.wordsCount || 0),
            visitorCount: testData.participantCount || 0,
            testCount: testData.testResultCount || 0,
            visitors: testData.visitors || [],
            results: testData.results || [],
            libraryId: testData.libraryId,
            libraryName: testData.libraryName,
            isExpired: testData.isExpired,

            // 多组任务相关信息
            isMultiLevel: testData.isMultiLevel || false,
            totalLevels: testData.totalLevels || 1,
            totalGroups: testData.totalGroups || testData.totalLevels || 1,
            wordsPerGroup: testData.wordsPerGroup || 20,
            levelProgress: testData.levelProgress || {},

            // 使用云端计算的统计数据
            averageScore: testData.stats ? testData.stats.averageScore : 0,
            lastTestTime: testData.stats ? testData.stats.recentActivity : null,
            lastShareTime: testData.createTime,

            // 添加详细统计信息
            stats: testData.stats || {}
          }));

          console.log('从云端获取到分享测试数据:', myShareTests.length, '个');
        }
      } catch (cloudError) {
        console.log('从云端获取分享数据失败，尝试本地数据:', cloudError);
      }

      // 如果云端没有数据或失败，或者强制刷新时，从本地存储获取分享测试数据
      if (myShareTests.length === 0 || forceRefresh) {
        if (forceRefresh) {
          console.log('强制刷新模式：清空本地缓存，重新获取数据');
          // 清空本地缓存
          wx.removeStorageSync('shareTests');
        }
        const shareTests = wx.getStorageSync('shareTests') || {};

        // 筛选当前用户创建的分享测试
        Object.values(shareTests).forEach(testData => {
          if (testData.creatorInfo?.openid === currentUserOpenid ||
              testData.createdBy === currentUser.nickName) {

            // 统计访问者和测试数据
            const visitors = testData.visitors || [];
            const results = testData.results || [];

            const shareTestInfo = {
              shareId: testData.shareId,
              testMode: testData.testMode || 'unknown',
              createTime: testData.createTime || testData.createdAt,
              wordCount: testData.words ? testData.words.length : (testData.wordsCount || 0),
              visitorCount: visitors.length,
              testCount: results.length,
              visitors: visitors,
              results: results,
              libraryId: testData.libraryId,
              libraryName: testData.libraryName,

              // 多组任务相关信息
              isMultiLevel: testData.isMultiLevel || false,
              totalLevels: testData.totalLevels || 1,
              totalGroups: testData.totalGroups || testData.totalLevels || 1,
              wordsPerGroup: testData.wordsPerGroup || 20,
              levelProgress: testData.levelProgress || {},

              // 计算统计数据
              averageScore: this.calculateAverageScore(results),
              lastTestTime: this.getLastTestTime(results),
              lastShareTime: testData.lastShareTime || testData.createTime
            };

            myShareTests.push(shareTestInfo);
          }
        });

        console.log('从本地获取到分享测试数据:', myShareTests.length, '个');
      }

      // 过期检查和清理
      const now = new Date();
      const validTests = [];
      const expiredTests = [];
      const soonExpireTests = [];

      myShareTests.forEach(test => {
        if (test.expireTime) {
          const expireTime = new Date(test.expireTime);
          const timeDiff = expireTime.getTime() - now.getTime();
          const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

          if (timeDiff <= 0) {
            // 已过期
            expiredTests.push(test);
          } else if (daysDiff <= 3) {
            // 即将过期（3天内）
            test.soonExpire = true;
            test.expireDays = daysDiff;
            validTests.push(test);
            soonExpireTests.push(test);
          } else {
            validTests.push(test);
          }
        } else {
          validTests.push(test);
        }
      });

      // 如果有过期的测试，自动删除
      if (expiredTests.length > 0) {
        console.log('发现过期测试:', expiredTests.length, '个，自动删除');
        this.deleteExpiredTests(expiredTests);
      }

      // 如果有即将过期的测试，显示提醒
      if (soonExpireTests.length > 0) {
        console.log('发现即将过期测试:', soonExpireTests.length, '个');
      }

      // 保存所有有效测试数据
      const totalCount = validTests.length;

      // 先处理所有数据进行分组，不进行分页
      const allModeGroups = this.processShareData(validTests);

      // 计算分页信息（基于总数据量）
      const totalPages = Math.ceil(totalCount / this.data.pageSize);
      const showPagination = totalCount > this.data.pageSize;

      this.setData({
        modeGroups: allModeGroups, // 显示所有分组数据
        shareTestsByMode: this.groupByTestMode(validTests), // 保留兼容性，使用所有数据
        loading: false,
        isEmpty: totalCount === 0,
        selectedTests: [], // 强制清空选中状态
        isEditMode: false,  // 强制退出编辑模式
        // 分页相关数据
        allShareTests: validTests,
        totalCount: totalCount,
        totalPages: totalPages,
        showPagination: showPagination
      });

    } catch (error) {
      console.error('加载分享测试失败:', error);
      this.setData({
        shareTestsByMode: {},
        modeGroups: [],
        loading: false,
        isEmpty: true
      });
    }

    wx.hideLoading();
  },

  // 删除过期测试
  async deleteExpiredTests(expiredTests) {
    try {
      const shareIds = expiredTests.map(test => test.shareId);

      // 调用云函数删除过期测试
      await wx.cloud.callFunction({
        name: 'deleteShareTests',
        data: { shareIds: shareIds }
      });

      console.log('过期测试删除成功:', shareIds.length, '个');
    } catch (error) {
      console.error('删除过期测试失败:', error);
    }
  },

  // 分页相关函数
  goToPage(page) {
    if (page < 1 || page > this.data.totalPages) return;

    this.setData({
      currentPage: page
    });

    this.loadShareTests();
  },

  // 上一页
  prevPage() {
    if (this.data.currentPage > 1) {
      this.goToPage(this.data.currentPage - 1);
    }
  },

  // 下一页
  nextPage() {
    if (this.data.currentPage < this.data.totalPages) {
      this.goToPage(this.data.currentPage + 1);
    }
  },

  // 跳转到指定页码
  onJumpToPageInput(e) {
    this.setData({
      jumpToPage: e.detail.value
    });
  },

  // 确认跳转
  confirmJumpToPage() {
    const page = parseInt(this.data.jumpToPage);
    if (page && page >= 1 && page <= this.data.totalPages) {
      this.goToPage(page);
      this.setData({
        jumpToPage: ''
      });
    } else {
      wx.showToast({
        title: '页码无效',
        icon: 'none'
      });
    }
  },

  /**
   * 按测试模式分组
   */
  groupByTestMode(shareTests) {
    const grouped = {};
    
    shareTests.forEach(test => {
      const mode = test.testMode;
      if (!grouped[mode]) {
        grouped[mode] = {
          mode: mode,
          modeText: this.getTestModeText(mode),
          modeIcon: this.getTestModeIcon(mode),
          tests: [],
          totalTests: 0,
          totalVisitors: 0,
          totalTestCount: 0
        };
      }
      
      grouped[mode].tests.push(test);
      grouped[mode].totalTests++;
      grouped[mode].totalVisitors += test.visitorCount;
      grouped[mode].totalTestCount += test.testCount;
    });

    // 对每个模式下的测试按创建时间排序
    Object.values(grouped).forEach(modeGroup => {
      modeGroup.tests.sort((a, b) => (b.createTime || 0) - (a.createTime || 0));
    });

    return grouped;
  },

  /**
   * 计算平均分数
   */
  calculateAverageScore(results) {
    if (!results || results.length === 0) return 0;
    
    const totalScore = results.reduce((sum, result) => {
      return sum + (result.score || 0);
    }, 0);
    
    return Math.round(totalScore / results.length);
  },

  /**
   * 获取最后测试时间
   */
  getLastTestTime(results) {
    if (!results || results.length === 0) return 0;
    
    return Math.max(...results.map(result => result.timestamp || 0));
  },

  /**
   * 获取测试模式文本
   */
  getTestModeText(testMode) {
    // 标准化模式名称
    const normalizedMode = this.normalizeTestMode(testMode);
    const configs = this.getModeConfig();
    
    if (configs[normalizedMode]) {
      return configs[normalizedMode].name;
    }
    
    // 兼容旧的模式名称
    const modeMap = {
      'en_to_cn': '英译汉',
      'cn_to_en': '汉译英',
      'phrase_en2zh': '短语测试',
      'phrase_zh2en': '短语测试',
      'dictation': '听写',
      'elimination': '消消乐',
      'spelling': '听写',
      'puzzle': '消消乐',
      'custom': '单词检测'
    };

    return modeMap[testMode] || '英译汉'; // 默认返回英译汉而不是"未知模式"
  },

  /**
   * 获取测试模式图标
   */
  getTestModeIcon(testMode) {
    // 标准化模式名称
    const normalizedMode = this.normalizeTestMode(testMode);
    const configs = this.getModeConfig();
    
    if (configs[normalizedMode]) {
      return configs[normalizedMode].icon;
    }
    
    // 兼容旧的模式名称
    const iconMap = {
      'en_to_cn': '🈯',
      'cn_to_en': '🈲',
      'phrase_en2zh': '📝',
      'phrase_zh2en': '📝',
      'dictation': '🎧',
      'elimination': '🧩',
      'spelling': '🎧',
      'puzzle': '🧩',
      'custom': '🔍'
    };

    return iconMap[testMode] || '🈯'; // 默认返回英译汉图标
  },

  /**
   * 切换模式展开/折叠
   */
  toggleModeExpand(e) {
    const mode = e.currentTarget.dataset.mode;
    const modeGroups = [...this.data.modeGroups];
    
    const targetGroup = modeGroups.find(group => group.mode === mode);
    if (targetGroup) {
      targetGroup.expanded = !targetGroup.expanded;
    }
    
    this.setData({
      modeGroups: modeGroups
    });
  },

  /**
   * 查看访问者
   */
  async viewVisitors(e) {
    const shareId = e.currentTarget.dataset.shareId;
    console.log('查看访问者，分享ID:', shareId);

    if (!shareId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '加载中...' });

    try {
      // 优先从云端获取分享测试数据
      let shareTestData = null;

      try {
        const result = await wx.cloud.callFunction({
          name: 'getShareTest',
          data: { shareId: shareId }
        });

        if (result.result.success) {
          shareTestData = result.result.data;
        }
      } catch (cloudError) {
        console.log('从云端获取分享数据失败:', cloudError);
      }

      // 如果云端没有数据，尝试从本地获取
      if (!shareTestData) {
        const shareTests = wx.getStorageSync('shareTests') || {};
        shareTestData = shareTests[shareId];
      }

      wx.hideLoading();

      if (!shareTestData) {
        wx.showToast({
          title: '分享测试不存在',
          icon: 'none'
        });
        return;
      }

      // 检查访问者数据（云端数据结构可能不同）
      const visitors = shareTestData.visitors || [];
      const results = shareTestData.results || [];
      const participantCount = shareTestData.participantCount || 0;

      // 如果没有访问者和结果数据，显示提示
      if (visitors.length === 0 && results.length === 0 && participantCount === 0) {
        wx.showModal({
          title: '暂无访问者',
          content: '该分享测试还没有人参与，分享链接给朋友们来测试吧！',
          showCancel: false,
          confirmText: '知道了'
        });
        return;
      }

      // 跳转到分享管理页面（显示进度详情）
      wx.navigateTo({
        url: `/pages/profile/share-management/share-management?shareId=${shareId}`,
        fail: (err) => {
          console.log('跳转失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      wx.hideLoading();
      console.error('检查分享测试数据失败:', error);
      wx.showToast({
        title: '数据读取失败',
        icon: 'none'
      });
    }
  },

  /**
   * 复制分享链接
   */
  copyShareLink(e) {
    const { shareId, testMode } = e.currentTarget.dataset;
    
    const shareText = `墨词自习室测试邀请\n\n测试ID: ${shareId}\n模式: ${this.getTestModeText(testMode)}\n\n请在"墨词自习室"小程序中，进入"我的"->"收到的分享"，输入测试ID参与测试。`;
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '分享内容已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 分享到微信
   */
  shareToWeChat(e) {
    const { shareId, testMode, wordCount } = e.currentTarget.dataset;
    
    const modeText = this.getTestModeText(testMode);
    const modeIcon = this.getTestModeIcon(testMode);
    
    const itemText = (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') ? '短语' : '单词';
    this.setData({
      currentShareData: {
        title: `${modeIcon} ${modeText} - ${wordCount}个${itemText}`,
        path: this.getSharePath(testMode, shareId),
        imageUrl: '/assets/icons/logo.png'
      },
      showShareModal: true
    });
  },

  /**
   * 获取分享路径
   */
  getSharePath(testMode, shareId) {
    if (testMode === 'elimination') {
      return `pages/task/puzzle/puzzle?shareId=${shareId}&isShared=true&mode=custom`;
    } else if (testMode === 'dictation') {
      return `pages/spelling/practice/practice?shareId=${shareId}&shareMode=share`;
    } else if (testMode === 'phrase_en2zh' || testMode === 'phrase_zh2en') {
      // 短语测试需要传递isPhrase参数
      const actualTestMode = testMode === 'phrase_en2zh' ? 'en_to_cn' : 'cn_to_en';
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share&testMode=${actualTestMode}&isPhrase=true`;
    } else {
      return `pages/wordtest/test/test?shareId=${shareId}&shareMode=share`;
    }
  },

  /**
   * 长按处理 - 进入编辑模式或显示操作菜单
   */
  onShareTestLongPress(e) {
    const { shareId, testMode } = e.currentTarget.dataset;
    const testModeText = this.getTestModeText(testMode);
    
    wx.showActionSheet({
      itemList: ['删除分享', '复制链接', '分享到微信'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 直接调用删除逻辑，不需要事件对象
            const modeText = this.getTestModeText(testMode);
            wx.showModal({
              title: '删除确认',
              content: `确定要删除这个${modeText}测试吗？删除后无法恢复。`,
              confirmColor: '#ff6b6b',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  this.doDeleteShareTest(shareId);
                }
              }
            });
            break;
          case 1:
            this.copyShareLink(e);
            break;
          case 2:
            this.shareToWeChat(e);
            break;
        }
      }
    });
  },

  /**
   * 删除单个分享测试
   */
  deleteShareTest(e) {
    const shareId = e.currentTarget.dataset.shareId;
    const testMode = e.currentTarget.dataset.testMode;
    const modeText = this.getTestModeText(testMode);
    
    wx.showModal({
      title: '删除确认',
      content: `确定要删除这个${modeText}测试吗？删除后无法恢复。`,
      confirmColor: '#ff6b6b',
      success: (res) => {
        if (res.confirm) {
          this.doDeleteShareTest(shareId);
        }
      }
    });
  },

  /**
   * 执行删除分享测试
   */
  async doDeleteShareTest(shareId) {
    wx.showLoading({ title: '删除中...' });

    try {
      // 优先从云端删除
      let cloudDeleteSuccess = false;

      try {
        const result = await wx.cloud.callFunction({
          name: 'deleteShareTest',
          data: {
            shareIds: [shareId],
            type: 'creator'
          }
        });

        if (result.result.success) {
          cloudDeleteSuccess = true;
        }
      } catch (cloudError) {
        console.log('从云端删除分享测试失败:', cloudError);
      }

      // 同时从本地存储中删除（兼容旧数据）
      const shareTests = wx.getStorageSync('shareTests') || {};
      const localExists = shareTests[shareId];
      if (localExists) {
        delete shareTests[shareId];
        wx.setStorageSync('shareTests', shareTests);
      }

      wx.hideLoading();

      if (cloudDeleteSuccess || localExists) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });

        // 重新加载数据
        this.loadShareTests();
      } else {
        wx.showToast({
          title: '测试不存在',
          icon: 'none'
        });
      }

    } catch (error) {
      wx.hideLoading();
      console.error('删除分享测试失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    }
  },

  /**
   * 切换编辑模式
   */
  toggleEditMode() {
    this.setData({
      isEditMode: !this.data.isEditMode,
      selectedTests: []
    });
  },



  /**
   * 选择/取消选择测试
   */
  toggleSelectTest(e) {
    const shareId = e.currentTarget.dataset.shareId;
    let selectedTests = [...this.data.selectedTests];
    
    if (selectedTests.includes(shareId)) {
      selectedTests = selectedTests.filter(id => id !== shareId);
    } else {
      selectedTests.push(shareId);
    }
    
    this.setData({
      selectedTests: selectedTests
    }, () => {
      // 更新测试数据中的选中状态
      this.updateTestSelectionState();
    });
  },

  /**
   * 更新测试选中状态
   */
  updateTestSelectionState() {
    const modeGroups = this.data.modeGroups.map(group => ({
      ...group,
      tests: group.tests.map(test => ({
        ...test,
        isSelected: this.data.selectedTests.indexOf(test.shareId) > -1
      }))
    }));
    
    this.setData({ modeGroups });
  },

  /**
   * 批量删除选中的测试
   */
  async batchDeleteTests() {
    const { selectedTests } = this.data;
    if (selectedTests.length === 0) {
      wx.showToast({
        title: '请选择要删除的测试',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '批量删除',
      content: `确定要删除选中的 ${selectedTests.length} 个分享测试吗？删除后无法恢复。`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });

          try {
            // 优先从云端批量删除
            let cloudDeleteSuccess = false;

            try {
              const result = await wx.cloud.callFunction({
                name: 'deleteShareTest',
                data: {
                  shareIds: selectedTests,
                  type: 'creator'
                }
              });

              if (result.result.success) {
                cloudDeleteSuccess = true;
              }
            } catch (cloudError) {
              console.log('从云端批量删除分享测试失败:', cloudError);
            }

            // 同时从本地存储中删除（兼容旧数据）
            const shareTests = wx.getStorageSync('shareTests') || {};
            selectedTests.forEach(shareId => {
              delete shareTests[shareId];
            });
            wx.setStorageSync('shareTests', shareTests);

            wx.hideLoading();

            wx.showToast({
              title: '批量删除成功',
              icon: 'success'
            });

            this.setData({
              isEditMode: false,
              selectedTests: []
            });

            // 重新加载数据
            this.loadShareTests();

          } catch (error) {
            wx.hideLoading();
            console.error('批量删除失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 清空某个模式下的所有分享
   */
  clearModeTests(e) {
    const mode = e.currentTarget.dataset.mode;
    const modeText = this.getTestModeText(mode);
    
    // 从新数据结构中查找模式组
    const modeGroup = this.data.modeGroups.find(group => group.mode === mode);
    
    if (!modeGroup || modeGroup.tests.length === 0) {
      wx.showToast({
        title: '该模式下没有测试',
        icon: 'none'
      });
      return;
    }
    
    wx.showModal({
      title: '清空确认',
      content: `确定要清空所有${modeText}测试(${modeGroup.tests.length}个)吗？删除后无法恢复。`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '清空中...',
            mask: true
          });

          try {
            // 获取要删除的分享ID列表
            const shareIds = modeGroup.tests.map(test => test.shareId);
            console.log('要删除的分享ID列表:', shareIds);
            console.log('模式组数据:', modeGroup);

            // 优先删除云端数据
            try {
              const cloudResult = await wx.cloud.callFunction({
                name: 'deleteShareTest',
                data: {
                  shareIds: shareIds,
                  type: 'creator'
                }
              });
              console.log('云端清空成功:', cloudResult.result);

              // 立即更新界面数据 - 从当前显示的数据中移除已删除的项目
              let updatedShareTests = this.data.shareTests ? [...this.data.shareTests] : [];
              shareIds.forEach(shareId => {
                const index = updatedShareTests.findIndex(test => test.shareId === shareId);
                if (index !== -1) {
                  updatedShareTests.splice(index, 1);
                  console.log('从界面数据中移除:', shareId);
                }
              });

              // 更新对应模式组的数据
              let updatedModeGroups = this.data.modeGroups ? [...this.data.modeGroups] : [];
              const modeGroupIndex = updatedModeGroups.findIndex(group => group.mode === modeGroup.mode);
              if (modeGroupIndex !== -1) {
                const filteredTests = updatedShareTests.filter(test => test.testMode === modeGroup.mode);
                updatedModeGroups[modeGroupIndex] = {
                  ...updatedModeGroups[modeGroupIndex],
                  tests: filteredTests,
                  count: filteredTests.length
                };
              }

              // 立即更新界面
              this.setData({
                shareTests: updatedShareTests,
                modeGroups: updatedModeGroups
              });

              console.log('界面数据已更新，剩余测试数量:', updatedShareTests.length);

            } catch (cloudError) {
              console.log('云端清空失败，继续本地清空:', cloudError);
            }

            // 删除本地数据
            const shareTests = wx.getStorageSync('shareTests') || {};
            console.log('清空前本地数据:', Object.keys(shareTests).length, '个');
            console.log('本地数据keys:', Object.keys(shareTests).slice(0, 5)); // 显示前5个key

            let deletedCount = 0;
            modeGroup.tests.forEach(test => {
              console.log('尝试删除shareId:', test.shareId, '存在:', !!shareTests[test.shareId]);
              if (shareTests[test.shareId]) {
                delete shareTests[test.shareId];
                deletedCount++;
              }
            });

            wx.setStorageSync('shareTests', shareTests);
            console.log('清空后本地数据:', Object.keys(shareTests).length, '个，删除了', deletedCount, '个');

            wx.hideLoading();
            wx.showToast({
              title: '清空成功',
              icon: 'success'
            });

            // 重新加载数据以确保数据同步
            setTimeout(() => {
              this.loadShareTests(true); // 强制刷新
            }, 1000);

          } catch (error) {
            wx.hideLoading();
            console.error('清空失败:', error);
            wx.showToast({
              title: '清空失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 关闭分享弹窗
   */
  closeShareModal() {
    this.setData({
      showShareModal: false
    });
  },

  /**
   * 分享按钮点击
   */
  onShareButtonTap() {
    console.log('分享按钮被点击');
    this.setData({
      showShareModal: false
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp) {
    if (!timestamp) return '未知时间';
    
    const now = new Date();
    const time = new Date(timestamp);
    const diff = now - time;
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚';
    }
    
    // 小于1小时
    if (diff < 3600000) {
      return Math.floor(diff / 60000) + '分钟前';
    }
    
    // 小于1天
    if (diff < ********) {
      return Math.floor(diff / 3600000) + '小时前';
    }
    
    // 小于7天
    if (diff < *********) {
      return Math.floor(diff / ********) + '天前';
    }
    
    // 超过7天，显示具体日期
    return time.getFullYear() + '/' + 
           String(time.getMonth() + 1).padStart(2, '0') + '/' + 
           String(time.getDate()).padStart(2, '0');
  },

  /**
   * 跳转到创建分享页面
   */
  goToCreateShare() {
    wx.switchTab({
      url: '/pages/wordbank/wordbank',
      success: () => {
        console.log('跳转到词库选择页面成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { currentShareData } = this.data;
    
    if (currentShareData) {
      return {
        title: currentShareData.title,
        path: currentShareData.path,
        imageUrl: currentShareData.imageUrl
      };
    }
    
    return {
      title: '墨词自习室 - 我的分享管理',
      path: '/pages/profile/share/share',
      imageUrl: '/assets/icons/logo.png'
    };
  },

  // 定义模式映射
  getModeConfig() {
    return {
      'translate': {
        name: '英译汉',
        icon: '🈯',
        validModes: ['translate', 'english-chinese', 'en_to_cn']
      },
      'chinese': {
        name: '汉译英',
        icon: '🈲',
        validModes: ['chinese', 'chinese-english', 'cn_to_en']
      },
      'dictation': {
        name: '听写',
        icon: '🎧',
        validModes: ['dictation', 'spelling']
      },
      'puzzle': {
        name: '消消乐',
        icon: '🧩',
        validModes: ['puzzle', 'elimination']
      },
      'custom': {
        name: '单词检测',
        icon: '🔍',
        validModes: ['custom']
      },
      'phrase': {
        name: '短语测试',
        icon: '📝',
        validModes: ['phrase_en2zh', 'phrase_zh2en', 'phrase']
      }
    };
  },

  // 标准化测试模式
  normalizeTestMode(testMode) {
    if (!testMode) return 'translate'; // 默认为英译汉
    
    const configs = this.getModeConfig();
    
    // 直接匹配
    if (configs[testMode]) {
      return testMode;
    }
    
    // 查找别名匹配
    for (const [mode, config] of Object.entries(configs)) {
      if (config.validModes.includes(testMode)) {
        return mode;
      }
    }
    
    // 模糊匹配
    const lowerMode = testMode.toLowerCase();
    if (lowerMode.includes('custom') || lowerMode.includes('检测')) {
      return 'custom';
    }
    if (lowerMode.includes('phrase') || lowerMode.includes('短语')) {
      return 'phrase';
    }
    if (lowerMode.includes('chinese') || lowerMode.includes('汉译') || lowerMode.includes('中译') || lowerMode.includes('cn_to_en')) {
      return 'chinese';
    }
    if (lowerMode.includes('dictation') || lowerMode.includes('听写') || lowerMode.includes('spell')) {
      return 'dictation';
    }
    if (lowerMode.includes('puzzle') || lowerMode.includes('消消乐') || lowerMode.includes('elimination')) {
      return 'puzzle';
    }

    // 默认返回英译汉
    return 'translate';
  },

  // 处理分享数据
  processShareData(shareTests) {
    const configs = this.getModeConfig();
    const groups = {};
    
    // 初始化所有模式分组
    Object.keys(configs).forEach(mode => {
      groups[mode] = {
        mode,
        name: configs[mode].name,
        icon: configs[mode].icon,
        tests: [],
        expanded: true, // 默认展开
        stats: {
          totalTests: 0,
          totalVisitors: 0,
          totalAttempts: 0
        }
      };
    });
    
    shareTests.forEach(test => {
      // 标准化测试模式
      const normalizedMode = this.normalizeTestMode(test.testMode);
      test.testMode = normalizedMode;
      
      // 确保测试数据完整性
      if (!test.visitors) test.visitors = [];
      if (!test.creatorInfo) test.creatorInfo = {};
      if (!test.config) test.config = {};
      
      // 计算统计数据
      const visitorsCount = test.visitors.length;
      const attemptsCount = test.visitors.reduce((sum, visitor) => {
        return sum + (visitor.attempts ? visitor.attempts.length : 0);
      }, 0);
      
      // 添加到对应分组
      groups[normalizedMode].tests.push(test);
      groups[normalizedMode].stats.totalTests++;
      groups[normalizedMode].stats.totalVisitors += visitorsCount;
      groups[normalizedMode].stats.totalAttempts += attemptsCount;
    });
    
    // 只返回有测试的分组
    const result = Object.values(groups).filter(group => group.tests.length > 0);
    
    return result;
  },
})