<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">写作积累</view>
    <view class="subtitle">提升英语写作能力</view>
  </view>

  <!-- 功能模块卡片 -->
  <view class="modules-container">
    <view class="section-title">学习模块</view>
    <view class="modules-grid">
      <view 
        class="module-card" 
        wx:for="{{modules}}" 
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onModuleSelect"
      >
        <view class="card-header">
          <view class="module-icon">{{item.icon}}</view>
          <view class="module-info">
            <view class="module-title">{{item.title}}</view>
            <view class="module-subtitle">{{item.subtitle}}</view>
          </view>
        </view>
        
        <view class="card-content">
          <view class="module-description">{{item.description}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 话题预览 -->
  <view class="topics-preview">
    <view class="section-title">涵盖话题</view>
    <view class="topics-grid">
      <view class="topic-tag" wx:for="{{topics}}" wx:key="*this">
        <text class="topic-text">{{item}}</text>
      </view>
    </view>
  </view>

  <!-- 学习贴士 -->
  <view class="tips-section">
    <view class="section-title">学习贴士</view>
    <view class="tips-container">
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">建议先学习话题素材，积累词汇和表达</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">通过范文学习写作结构和技巧</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">定期练习写作，巩固所学内容</text>
      </view>
    </view>
  </view>

  <!-- 开发提示 -->
  <view class="development-notice">
    <view class="notice-icon">🚧</view>
    <view class="notice-text">
      <text class="notice-title">注意：</text>
      <text class="notice-content">这只是示例内容，不是正式内容，具体内容正在完善中</text>
    </view>
  </view>
</view> 