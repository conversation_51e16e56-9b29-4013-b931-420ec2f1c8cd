// 云函数入口文件
const cloud = require('wx-server-sdk')
const crypto = require('crypto')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const usersCollection = db.collection('users')

// 生成token
function generateToken() {
  return crypto.randomBytes(32).toString('hex')
}

// 密码加密
function encryptPassword(password) {
  if (typeof password !== 'string' || !password) {
    throw new Error('密码不能为空');
  }
  return crypto.createHash('sha256').update(password).digest('hex')
}

// 云函数入口函数
exports.main = async (event, context) => {
  const { username, password } = event

  if (!username || !password) {
    return {
      code: 400,
      message: '用户名或密码不能为空'
    }
  }

  try {
    // 检查用户名是否已存在
    const existUser = await usersCollection.where({
      username: username
    }).get()

    if (existUser.data.length > 0) {
      return {
        code: 400,
        message: '用户名已存在'
      }
    }

    const now = new Date()
    const token = generateToken()

    // 创建新用户
    const result = await usersCollection.add({
      data: {
        username: username,
        password: encryptPassword(password),
        token: token,
        createTime: now,
        lastLoginTime: now,
        settings: {
          dailyWords: 20,
          reminderEnabled: false,
          reminderTime: '20:00',
          autoPlayAudio: true,
          showPhonetic: true,
          showExample: true
        },
        stats: {
          totalWords: 0,
          learnedWords: 0,
          reviewWords: 0,
          mistakeWords: 0,
          continuousDays: 0,
          lastLearnDate: null
        },
        membership: {
          isVip: false,
          expireTime: null,
          type: 'free'
        }
      }
    })

    return {
      code: 200,
      message: '注册成功',
      data: {
        token: token,
        userInfo: {
          _id: result._id,
          username: username,
          settings: {
            dailyWords: 20,
            reminderEnabled: false,
            reminderTime: '20:00',
            autoPlayAudio: true,
            showPhonetic: true,
            showExample: true
          },
          stats: {
            totalWords: 0,
            learnedWords: 0,
            reviewWords: 0,
            mistakeWords: 0,
            continuousDays: 0,
            lastLearnDate: null
          },
          membership: {
            isVip: false,
            expireTime: null,
            type: 'free'
          }
        }
      }
    }
  } catch (error) {
    console.error(error)
    return {
      code: 500,
      message: '服务器错误'
    }
  }
} 