<view class="container">
  <!-- 页面头部 -->
  <view class="header">
    <view class="title">英语名著与小说</view>
    <view class="subtitle">品读经典，提升文学素养</view>
  </view>

  <!-- 小说列表 -->
  <view class="novels-container">
    <view class="section-title">经典选读</view>
    <view class="novels-grid">
      <view 
        class="novel-card"
        wx:for="{{novels}}"
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onNovelSelect"
      >
        <view class="card-content {{item.color}}">
          <view class="novel-header">
            <view class="novel-title">{{item.title}}</view>
            <view class="english-title">{{item.englishTitle}}</view>
            <view class="author">{{item.author}}</view>
          </view>
          
          <view class="novel-info">
            <view class="info-tags">
              <text class="difficulty-tag">{{item.difficulty}}</text>
              <text class="time-tag">{{item.readTime}}</text>
            </view>
            <view class="description">{{item.description}}</view>
          </view>
          
          <view class="card-footer">
            <view class="read-btn">
              <text>开始阅读</text>
              <text class="arrow">→</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 阅读说明 -->
  <view class="reading-guide">
    <view class="guide-title">阅读说明</view>
    <view class="guide-content">
      <view class="guide-item">
        <text class="guide-dot">•</text>
        <text class="guide-text">每部作品提供精选片段，中英对照展示</text>
      </view>
      <view class="guide-item">
        <text class="guide-dot">•</text>
        <text class="guide-text">点击段落可切换显示中文翻译</text>
      </view>
      <view class="guide-item">
        <text class="guide-dot">•</text>
        <text class="guide-text">建议先尝试理解英文原文，再参考中文对照</text>
      </view>
      <view class="guide-item">
        <text class="guide-dot">•</text>
        <text class="guide-text">体会不同作家的写作风格和表达技巧</text>
      </view>
    </view>
  </view>

  <!-- 开发提示 -->
  <view class="development-notice">
    <view class="notice-icon">📚</view>
    <view class="notice-text">
      <text class="notice-title">提示：</text>
      <text class="notice-content">这些是示例内容片段，完整内容正在完善中</text>
    </view>
  </view>
</view> 