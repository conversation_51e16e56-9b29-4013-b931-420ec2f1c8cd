<view class="container">
  <!-- 文章头部信息 -->
  <view class="article-header" wx:if="{{article}}">
    <view class="article-title">{{article.title}}</view>
    <view class="article-meta" wx:if="{{article.source}}">
      <text class="meta-source">{{article.source}}</text>
      <text class="meta-divider" wx:if="{{article.date}}">•</text>
      <text class="meta-date" wx:if="{{article.date}}">{{article.date}}</text>
    </view>
    <view class="article-author" wx:if="{{article.author}}">
      <text class="author-label">作者：</text>
      <text class="author-name">{{article.author}}</text>
    </view>
  </view>

  <!-- 控制按钮 -->
  <view class="controls">
    <button class="control-btn" bindtap="onToggleAllChinese">
      <text class="btn-icon">{{showAllChinese ? '🙈' : '👁️'}}</text>
      <text class="btn-text">{{showAllChinese ? '隐藏所有中文' : '显示所有中文'}}</text>
    </button>
  </view>

  <!-- 文章内容 -->
  <view class="article-content">
    <view 
      class="paragraph-container"
      wx:for="{{paragraphs}}"
      wx:key="id"
      data-index="{{index}}"
      bindtap="onToggleParagraph"
    >
      <view class="paragraph-number">{{index + 1}}</view>
      <view class="paragraph-content">
        <view class="english-text">{{item.english}}</view>
        <view class="chinese-text {{item.showChinese ? 'show' : 'hide'}}" wx:if="{{item.chinese}}">
          {{item.chinese}}
        </view>
        <view class="tap-hint" wx:if="{{!item.showChinese && item.chinese}}">
          点击显示中文翻译
        </view>
      </view>
    </view>
  </view>

  <!-- 阅读提示 -->
  <view class="reading-tips">
    <view class="tips-title">阅读提示</view>
    <view class="tips-content">
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">点击段落可切换中文翻译显示</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">建议先阅读英文，思考后再看中文对照</text>
      </view>
      <view class="tip-item">
        <text class="tip-dot">•</text>
        <text class="tip-text">注意学习文中的词汇和表达方式</text>
      </view>
    </view>
  </view>
</view> 