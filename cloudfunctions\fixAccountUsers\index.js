// 修复账号密码注册用户数据的云函数
const cloud = require('wx-server-sdk')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()
const usersCollection = db.collection('users')

exports.main = async (event, context) => {
  console.log('开始修复账号密码注册用户数据...')
  
  try {
    // 查找所有用户
    const allUsers = await usersCollection.get()
    console.log('找到用户总数:', allUsers.data.length)
    
    let fixedCount = 0
    let accountUsersCount = 0
    
    for (const user of allUsers.data) {
      // 判断是否为账号密码注册用户
      // 1. 有password字段且不为空
      // 2. 没有openid或openid为空
      // 3. username不是wx_开头的格式
      const isAccountUser = user.password && 
                           user.password !== '' && 
                           (!user.openid || user.openid === '') &&
                           user.username &&
                           !user.username.startsWith('wx_')
      
      if (isAccountUser) {
        accountUsersCount++
        console.log('发现账号密码注册用户:', user.username)
        
        // 检查是否需要修复
        let needsFix = false
        let updateData = {}
        
        // 修复wechatInfo.nickName
        if (!user.wechatInfo || !user.wechatInfo.nickName || user.wechatInfo.nickName === '') {
          needsFix = true
          updateData['wechatInfo.nickName'] = user.username
          console.log('需要修复昵称:', user.username)
        }
        
        // 添加registrationType标记
        if (!user.registrationType) {
          needsFix = true
          updateData['registrationType'] = 'account'
          console.log('需要添加注册类型标记:', user.username)
        }
        
        // 确保wechatInfo.avatarUrl存在
        if (!user.wechatInfo || !user.wechatInfo.avatarUrl) {
          needsFix = true
          updateData['wechatInfo.avatarUrl'] = '/assets/icons/profile.png'
          console.log('需要修复头像:', user.username)
        }
        
        // 清除isNewUser标记（老用户）
        if (user.isNewUser === true) {
          needsFix = true
          updateData['isNewUser'] = false
          console.log('需要清除新用户标记:', user.username)
        }
        
        if (needsFix) {
          try {
            await usersCollection.doc(user._id).update({
              data: updateData
            })
            fixedCount++
            console.log('修复成功:', user.username, updateData)
          } catch (error) {
            console.error('修复失败:', user.username, error)
          }
        }
      }
    }
    
    console.log('修复完成!')
    console.log('账号密码注册用户总数:', accountUsersCount)
    console.log('修复用户数:', fixedCount)
    
    return {
      success: true,
      message: '修复完成',
      data: {
        totalUsers: allUsers.data.length,
        accountUsers: accountUsersCount,
        fixedUsers: fixedCount
      }
    }
    
  } catch (error) {
    console.error('修复过程中出错:', error)
    return {
      success: false,
      message: '修复失败',
      error: error.message
    }
  }
}
