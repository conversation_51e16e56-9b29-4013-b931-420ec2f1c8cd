/* 话题选择页面样式 */
.container {
  padding: 20rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  box-sizing: border-box;
}

/* 页面头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0 20rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.subtitle {
  font-size: 26rpx;
  color: #666;
}

/* 话题容器 */
.topics-container {
  margin-bottom: 40rpx;
}

.topics-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.topic-card {
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.topic-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.card-content {
  padding: 24rpx;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--topic-color-1), var(--topic-color-2));
  color: white;
}

/* 话题颜色主题 */
.card-content.blue {
  --topic-color-1: #4A90E2;
  --topic-color-2: #357ABD;
}

.card-content.green {
  --topic-color-1: #7ED321;
  --topic-color-2: #6CBE1A;
}

.card-content.purple {
  --topic-color-1: #8A2BE2;
  --topic-color-2: #6A1B9A;
}

.card-content.cyan {
  --topic-color-1: #50E3C2;
  --topic-color-2: #4ECDC4;
}

.card-content.emerald {
  --topic-color-1: #10B981;
  --topic-color-2: #059669;
}

.card-content.orange {
  --topic-color-1: #F59E0B;
  --topic-color-2: #D97706;
}

.card-content.pink {
  --topic-color-1: #EC4899;
  --topic-color-2: #DB2777;
}

.card-content.indigo {
  --topic-color-1: #6366F1;
  --topic-color-2: #4F46E5;
}

.card-content.red {
  --topic-color-1: #EF4444;
  --topic-color-2: #DC2626;
}

.card-content.sky {
  --topic-color-1: #0EA5E9;
  --topic-color-2: #0284C7;
}

.card-content.violet {
  --topic-color-1: #8B5CF6;
  --topic-color-2: #7C3AED;
}

.card-content.amber {
  --topic-color-1: #F59E0B;
  --topic-color-2: #D97706;
}

.topic-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.topic-info {
  flex: 1;
  margin-right: 16rpx;
}

.topic-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.topic-description {
  font-size: 24rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.arrow-icon {
  font-size: 24rpx;
  opacity: 0.8;
  flex-shrink: 0;
}

/* 开发提示 */
.development-notice {
  background: #fff3cd;
  border: 2rpx solid #ffeaa7;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.notice-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.notice-text {
  flex: 1;
}

.notice-title {
  font-weight: bold;
  color: #856404;
  font-size: 28rpx;
}

.notice-content {
  color: #856404;
  font-size: 26rpx;
} 